System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, instantiate, MyApp, Wave, GameIns, LevelDataEventTrigger, LevelDataEventTriggerType, LevelDataEventWaveGroup, LevelDataEventTriggerWave, _crd;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "../../game/wave/Wave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTrigger(extras) {
    _reporterNs.report("LevelDataEventTrigger", "./LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerType(extras) {
    _reporterNs.report("LevelDataEventTriggerType", "./LevelDataEventTrigger", _context.meta, extras);
  }

  _export({
    LevelDataEventWaveGroup: void 0,
    LevelDataEventTriggerWave: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      Wave = _unresolved_3.Wave;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      LevelDataEventTrigger = _unresolved_5.LevelDataEventTrigger;
      LevelDataEventTriggerType = _unresolved_5.LevelDataEventTriggerType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "79f93J9ZHpJhoBfHQa2Lb3c", "LevelDataEventTriggerWave", undefined);

      __checkObsolete__(['_decorator', 'instantiate', 'Prefab']);

      _export("LevelDataEventWaveGroup", LevelDataEventWaveGroup = class LevelDataEventWaveGroup {
        constructor() {
          this.waveUUID = [];
          this.weight = 50;
        }

      });

      _export("LevelDataEventTriggerWave", LevelDataEventTriggerWave = class LevelDataEventTriggerWave extends (_crd && LevelDataEventTrigger === void 0 ? (_reportPossibleCrUseOfLevelDataEventTrigger({
        error: Error()
      }), LevelDataEventTrigger) : LevelDataEventTrigger) {
        constructor() {
          super((_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave);
          this.waveGroup = [];
          this._selectedWaveGroup = null;
        }

        onInit() {
          // 提前创建好wave，但不执行
          if (this.waveGroup.length > 0) {
            let totalWeight = 0;
            this.waveGroup.forEach(waveGroup => {
              totalWeight += waveGroup.weight;
            });
            let randomWeight = Math.floor(Math.random() * totalWeight);
            let curWeight = 0;

            for (let waveGroup of this.waveGroup) {
              curWeight += waveGroup.weight;

              if (randomWeight <= curWeight) {
                this._selectedWaveGroup = waveGroup;
                break;
              }
            }
          }
        }

        onTrigger(x, y) {
          this._selectedWaveGroup.waveUUID.forEach(waveUUID => {
            const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.defaultBundleName, waveUUID);
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
              if (err) {
                console.error('LevelDataEventTriggerWave', " onInit load wave prefab err", err);
                return;
              }

              const waveComp = instantiate(prefab).getComponent(_crd && Wave === void 0 ? (_reportPossibleCrUseOfWave({
                error: Error()
              }), Wave) : Wave);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).waveManager.addWaveByLevel(waveComp, x, y);
            });
          });
        }

        fromJSON(obj) {
          super.fromJSON(obj);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fc90e596ba0e4d99ec535d35fbb42ea3cf0cbb79.js.map