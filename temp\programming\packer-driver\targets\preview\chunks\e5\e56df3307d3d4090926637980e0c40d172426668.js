System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Vec2, newCondition, newTrigger, LevelDataTerrain, LayerRandomRange, LevelDataScroll, LevelDataEmittier, LevelDataRandTerrains, LevelDataElem, LevelDataWaveGroup, LevelDataWave, LevelDataEvent, LevelDataLayer, LevelDataBackgroundLayer, LevelData, _crd, LayerType, LayerSplicingMode, LayerEimttierType;

  function _reportPossibleCrUseOfLevelDataEventCondtion(extras) {
    _reporterNs.report("LevelDataEventCondtion", "./condition/LevelDataEventCondtion", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTrigger(extras) {
    _reporterNs.report("LevelDataEventTrigger", "./trigger/LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfnewCondition(extras) {
    _reporterNs.report("newCondition", "./condition/newCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfnewTrigger(extras) {
    _reporterNs.report("newTrigger", "./trigger/newTrigger", _context.meta, extras);
  }

  _export({
    LevelDataTerrain: void 0,
    LayerRandomRange: void 0,
    LevelDataScroll: void 0,
    LevelDataEmittier: void 0,
    LevelDataRandTerrains: void 0,
    LevelDataElem: void 0,
    LevelDataWaveGroup: void 0,
    LevelDataWave: void 0,
    LevelDataEvent: void 0,
    LevelDataLayer: void 0,
    LevelDataBackgroundLayer: void 0,
    LevelData: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      newCondition = _unresolved_2.newCondition;
    }, function (_unresolved_3) {
      newTrigger = _unresolved_3.newTrigger;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "67408fV8zZMoasS26w+o0Er", "leveldata", undefined);

      // 注意：这里的分号是必须的，因为下面的代码是压缩过的
      __checkObsolete__(['Vec2']);

      _export("LayerType", LayerType = /*#__PURE__*/function (LayerType) {
        LayerType[LayerType["Background"] = 1] = "Background";
        LayerType[LayerType["Random"] = 2] = "Random";
        LayerType[LayerType["Scroll"] = 3] = "Scroll";
        LayerType[LayerType["Emittier"] = 4] = "Emittier";
        return LayerType;
      }({})); // 层级拼接方式


      _export("LayerSplicingMode", LayerSplicingMode = /*#__PURE__*/function (LayerSplicingMode) {
        LayerSplicingMode[LayerSplicingMode["node_height"] = 1] = "node_height";
        LayerSplicingMode[LayerSplicingMode["fix_height"] = 2] = "fix_height";
        LayerSplicingMode[LayerSplicingMode["random_height"] = 3] = "random_height";
        return LayerSplicingMode;
      }({})); // 地形发射体类型


      _export("LayerEimttierType", LayerEimttierType = /*#__PURE__*/function (LayerEimttierType) {
        LayerEimttierType[LayerEimttierType["Null"] = 0] = "Null";
        LayerEimttierType[LayerEimttierType["Infinite"] = 1] = "Infinite";
        LayerEimttierType[LayerEimttierType["Duration"] = 2] = "Duration";
        LayerEimttierType[LayerEimttierType["Count"] = 3] = "Count";
        LayerEimttierType[LayerEimttierType["Event"] = 4] = "Event";
        return LayerEimttierType;
      }({}));

      _export("LevelDataTerrain", LevelDataTerrain = class LevelDataTerrain {
        constructor() {
          this.uuid = "";
          this.position = new Vec2();
          this.scale = new Vec2();
          this.rotation = 0;
        }

      });

      _export("LayerRandomRange", LayerRandomRange = class LayerRandomRange {
        constructor() {
          this.min = 0;
          this.max = 0;
        }

      });

      _export("LevelDataScroll", LevelDataScroll = class LevelDataScroll {
        constructor() {
          this.weight = 100;
          this.uuids = [];
          this.splicingMode = LayerSplicingMode.node_height;
          this.offSetX = new LayerRandomRange();
          this.offSetY = new LayerRandomRange();
        }

      });

      _export("LevelDataEmittier", LevelDataEmittier = class LevelDataEmittier extends LevelDataTerrain {
        constructor() {
          super(...arguments);
          this.elemsUuid = [];
          this.type = LayerEimttierType.Null;
          this.value = 0;
          // 持续时间、发射次数、监听事件
          this.delay = 0;
          this.delayModity = new LayerRandomRange();
          this.interval = 0;
          this.intervalModity = new LayerRandomRange();
          this.angle = 0;
          this.angleModity = new LayerRandomRange();
          this.speed = 0;
          this.speedModity = new LayerRandomRange();
          this.offSetX = new LayerRandomRange();
        }

      });

      _export("LevelDataRandTerrains", LevelDataRandTerrains = class LevelDataRandTerrains extends LevelDataTerrain {
        constructor() {
          super(...arguments);
          this.weight = 100;
          this.terrains = [];
        }

      });

      _export("LevelDataElem", LevelDataElem = class LevelDataElem {
        constructor() {
          this.elemID = "";
          this.position = new Vec2();
          this.name = "default";
        }

      });

      _export("LevelDataWaveGroup", LevelDataWaveGroup = class LevelDataWaveGroup {
        constructor() {
          this.waveUUID = [];
          this.weight = 50;
        }

      });

      _export("LevelDataWave", LevelDataWave = class LevelDataWave extends LevelDataElem {
        constructor() {
          super(...arguments);
          this.waveGroup = [];
        }

        static fromJSON(json) {
          var wave = new LevelDataWave();
          if (!json) return wave;
          Object.assign(wave, json);
          return wave;
        }

      });

      _export("LevelDataEvent", LevelDataEvent = class LevelDataEvent extends LevelDataElem {
        constructor() {
          super(...arguments);
          this.conditions = [];
          this.triggers = [];
        }

        static fromJSON(json) {
          var _json$conditions, _json$triggers;

          var event = new LevelDataEvent();
          if (!json) return event;
          Object.assign(event, json);
          event.conditions = ((_json$conditions = json.conditions) == null ? void 0 : _json$conditions.map(condition => {
            return (_crd && newCondition === void 0 ? (_reportPossibleCrUseOfnewCondition({
              error: Error()
            }), newCondition) : newCondition)(condition);
          })) || [];
          event.triggers = ((_json$triggers = json.triggers) == null ? void 0 : _json$triggers.map(trigger => {
            return (_crd && newTrigger === void 0 ? (_reportPossibleCrUseOfnewTrigger({
              error: Error()
            }), newTrigger) : newTrigger)(trigger);
          })) || [];
          return event;
        }

      });

      _export("LevelDataLayer", LevelDataLayer = class LevelDataLayer {
        constructor() {
          this.remark = "";
          this.zIndex = 0;
          this.totalTime = 60;
          this.speed = 200;
          this.type = 0;
          // 对应LayerType
          this.terrains = [];
          this.scrolls = [];
          this.dynamics = [];
          this.emittiers = [];
          this.waves = [];
          this.events = [];
        }

        assign(json) {
          var _json$terrains, _json$scrolls, _json$dynamics, _json$emittiers, _json$waves, _json$events;

          Object.assign(this, json);
          this.terrains = ((_json$terrains = json.terrains) == null ? void 0 : _json$terrains.map(terrain => Object.assign(new LevelDataTerrain(), terrain))) || [];
          this.scrolls = ((_json$scrolls = json.scrolls) == null ? void 0 : _json$scrolls.map(scroll => Object.assign(new LevelDataScroll(), scroll))) || [];
          this.dynamics = ((_json$dynamics = json.dynamics) == null ? void 0 : _json$dynamics.map(dynamic => Object.assign(new LevelDataRandTerrains(), dynamic))) || [];
          this.emittiers = ((_json$emittiers = json.emittiers) == null ? void 0 : _json$emittiers.map(emittier => Object.assign(new LevelDataEmittier(), emittier))) || [];
          this.waves = ((_json$waves = json.waves) == null ? void 0 : _json$waves.map(wave => LevelDataWave.fromJSON(wave))) || [];
          this.events = ((_json$events = json.events) == null ? void 0 : _json$events.map(event => LevelDataEvent.fromJSON(event))) || [];
        }

        static fromJSON(json) {
          var layer = new LevelDataLayer();
          if (!json) return layer;
          layer.assign(json);
          return layer;
        }

      });

      _export("LevelDataBackgroundLayer", LevelDataBackgroundLayer = class LevelDataBackgroundLayer extends LevelDataLayer {
        constructor() {
          super(...arguments);
          this.backgrounds = [];
        }

        static fromJSON(json) {
          var layer = new LevelDataBackgroundLayer();
          if (!json) return layer;
          layer.assign(json);
          return layer;
        }

      });

      _export("LevelData", LevelData = class LevelData {
        constructor() {
          this.name = "";
          this.totalTime = 59;
          this.backgroundLayer = new LevelDataBackgroundLayer();
          this.floorLayers = [];
          this.skyLayers = [];
        }

        static fromJSON(json) {
          var _json$floorLayers, _json$skyLayers;

          var levelData = new LevelData();
          if (!json) return levelData;
          Object.assign(levelData, json);
          levelData.backgroundLayer = LevelDataBackgroundLayer.fromJSON(json.backgroundLayer);
          levelData.floorLayers = ((_json$floorLayers = json.floorLayers) == null ? void 0 : _json$floorLayers.map(layer => LevelDataLayer.fromJSON(layer))) || [];
          levelData.skyLayers = ((_json$skyLayers = json.skyLayers) == null ? void 0 : _json$skyLayers.map(layer => LevelDataLayer.fromJSON(layer))) || [];
          return levelData;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e56df3307d3d4090926637980e0c40d172426668.js.map