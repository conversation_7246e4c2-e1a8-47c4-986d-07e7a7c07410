{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EmitterData.ts"], "names": ["_decorator", "Prefab", "EventGroupData", "ExpressionValue", "ccclass", "property", "EmitterData", "displayName", "group", "visible", "tooltip", "type", "initialDelayStr", "initialDelay", "raw", "value", "emitDurationStr", "emitDuration", "preWarmDurationStr", "preWarmDuration", "loopIntervalStr", "loopInterval", "emitIntervalStr", "emitInterval", "emitPowerStr", "emitPower", "perEmitCountStr", "perEmitCount", "perEmitIntervalStr", "perEmitInterval", "perEmitOffsetXStr", "perEmitOffsetX", "angleStr", "angle", "countStr", "count", "arcStr", "arc", "radiusStr", "radius", "fromJSON", "json", "data", "Object", "assign", "eventGroupData", "map"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,M,OAAAA,M;;AAC7BC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,e,iBAAAA,e;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;AAE9B;AACA;AACA;AACA;;6BAEaM,W,WADZF,OAAO,CAAC,aAAD,C,UAIHC,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,WAAd;AAA2BC,QAAAA,KAAK,EAAE;AAAlC,OAAD,C,UAGRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,MAAd;AAAsBC,QAAAA,KAAK,EAAE;AAA7B,OAAD,C,UAIRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,SAAd;AAAyBC,QAAAA,KAAK,EAAE;AAAhC,OAAD,C,UAIRH,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,MAAd;AAAsBG,QAAAA,OAAO,EAAE,OAA/B;AAAwCF,QAAAA,KAAK,EAAE;AAA/C,OAAD,C,UAERH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,QAAd;AAAwBC,QAAAA,KAAK,EAAE;AAA/B,OAAD,C,WAKRH,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEV,MAAP;AAAeM,QAAAA,WAAW,EAAE,MAA5B;AAAoCC,QAAAA,KAAK,EAAE;AAA3C,OAAD,C,WAKRH,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,MAAd;AAAsBC,QAAAA,KAAK,EAAE;AAA7B,OAAD,C,WAGRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,MAAd;AAAsBG,QAAAA,OAAO,EAAE,qBAA/B;AAAsDF,QAAAA,KAAK,EAAE;AAA7D,OAAD,C,WAIRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,MAAd;AAAsBC,QAAAA,KAAK,EAAE;AAA7B,OAAD,C,WAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,MAAd;AAAsBC,QAAAA,KAAK,EAAE;AAA7B,OAAD,C,WAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,QAAd;AAAwBC,QAAAA,KAAK,EAAE;AAA/B,OAAD,C,WAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,cAAd;AAA8BC,QAAAA,KAAK,EAAE;AAArC,OAAD,C,WAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,eAAd;AAA+BC,QAAAA,KAAK,EAAE;AAAtC,OAAD,C,WAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,MAAd;AAAsBG,QAAAA,OAAO,EAAC,QAA9B;AAAwCF,QAAAA,KAAK,EAAE;AAA/C,OAAD,C,WAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,MAAd;AAAsBG,QAAAA,OAAO,EAAC,QAA9B;AAAwCF,QAAAA,KAAK,EAAE;AAA/C,OAAD,C,WAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,MAAd;AAAsBC,QAAAA,KAAK,EAAE;AAA7B,OAAD,C,WAKRH,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERJ,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,MAAd;AAAsBC,QAAAA,KAAK,EAAE;AAA7B,OAAD,C,WAIRH,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAACV,MAAN;AAAcM,QAAAA,WAAW,EAAE,MAA3B;AAAmCC,QAAAA,KAAK,EAAE;AAA1C,OAAD,C,WAGRH,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAC;AAAA;AAAA,6CAAN;AAAwBJ,QAAAA,WAAW,EAAE,KAArC;AAA4CC,QAAAA,KAAK,EAAE;AAAnD,OAAD,C,2BA3Gb,MACaF,WADb,CACyB;AAAA;AACrB;AACA;AAFqB;;AAIc;AAJd;;AAAA;;AAAA;;AAmBc;AAnBd;;AA0BrB;AA1BqB;;AA6BrB;AACA;AA9BqB;;AAiCc;AACnC;AAlCqB;;AAAA;;AA+CrB;AA/CqB;;AAsDrB;AAtDqB;;AA6DrB;AA7DqB;;AAoErB;AApEqB;;AA2ErB;AA3EqB;;AAkFrB;AAlFqB;;AAyFrB;AAzFqB;;AAgGrB;AAhGqB;;AAAA;;AAwGsB;AAxGtB;AAAA;;AASF,YAAfM,eAAe,GAAW;AAAE,iBAAO,KAAKC,YAAL,CAAkBC,GAAzB;AAA+B;;AAC5C,YAAfF,eAAe,CAACG,KAAD,EAAgB;AAAE,eAAKF,YAAL,CAAkBC,GAAlB,GAAwBC,KAAxB;AAAgC;;AAK3C,YAAfC,eAAe,GAAW;AAAE,iBAAO,KAAKC,YAAL,CAAkBH,GAAzB;AAA+B;;AAC5C,YAAfE,eAAe,CAACD,KAAD,EAAgB;AAAE,eAAKE,YAAL,CAAkBH,GAAlB,GAAwBC,KAAxB;AAAgC;;AAO/C,YAAlBG,kBAAkB,GAAW;AAAE,iBAAO,KAAKC,eAAL,CAAqBL,GAA5B;AAAkC;;AAC/C,YAAlBI,kBAAkB,CAACH,KAAD,EAAgB;AAAE,eAAKI,eAAL,CAAqBL,GAArB,GAA2BC,KAA3B;AAAmC;;AAcxD,YAAfK,eAAe,GAAW;AAAE,iBAAO,KAAKC,YAAL,CAAkBP,GAAzB;AAA+B;;AAC5C,YAAfM,eAAe,CAACL,KAAD,EAAgB;AAAE,eAAKM,YAAL,CAAkBP,GAAlB,GAAwBC,KAAxB;AAAgC;;AAKlD,YAAfO,eAAe,GAAW;AAAE,iBAAO,KAAKC,YAAL,CAAkBT,GAAzB;AAA+B;;AAC5C,YAAfQ,eAAe,CAACP,KAAD,EAAgB;AAAE,eAAKQ,YAAL,CAAkBT,GAAlB,GAAwBC,KAAxB;AAAgC;;AAMrD,YAAZS,YAAY,GAAW;AAAE,iBAAO,KAAKC,SAAL,CAAeX,GAAtB;AAA4B;;AACzC,YAAZU,YAAY,CAACT,KAAD,EAAgB;AAAE,eAAKU,SAAL,CAAeX,GAAf,GAAqBC,KAArB;AAA6B;;AAM5C,YAAfW,eAAe,GAAW;AAAE,iBAAO,KAAKC,YAAL,CAAkBb,GAAzB;AAA+B;;AAC5C,YAAfY,eAAe,CAACX,KAAD,EAAgB;AAAE,eAAKY,YAAL,CAAkBb,GAAlB,GAAwBC,KAAxB;AAAgC;;AAM/C,YAAlBa,kBAAkB,GAAW;AAAE,iBAAO,KAAKC,eAAL,CAAqBf,GAA5B;AAAkC;;AAC/C,YAAlBc,kBAAkB,CAACb,KAAD,EAAgB;AAAE,eAAKc,eAAL,CAAqBf,GAArB,GAA2BC,KAA3B;AAAmC;;AAMtD,YAAjBe,iBAAiB,GAAW;AAAE,iBAAO,KAAKC,cAAL,CAAoBjB,GAA3B;AAAiC;;AAC9C,YAAjBgB,iBAAiB,CAACf,KAAD,EAAgB;AAAE,eAAKgB,cAAL,CAAoBjB,GAApB,GAA0BC,KAA1B;AAAkC;;AAM7D,YAARiB,QAAQ,GAAW;AAAE,iBAAO,KAAKC,KAAL,CAAWnB,GAAlB;AAAwB;;AACrC,YAARkB,QAAQ,CAACjB,KAAD,EAAgB;AAAE,eAAKkB,KAAL,CAAWnB,GAAX,GAAiBC,KAAjB;AAAyB;;AAM3C,YAARmB,QAAQ,GAAW;AAAE,iBAAO,KAAKC,KAAL,CAAWrB,GAAlB;AAAwB;;AACrC,YAARoB,QAAQ,CAACnB,KAAD,EAAgB;AAAE,eAAKoB,KAAL,CAAWrB,GAAX,GAAiBC,KAAjB;AAAyB;;AAM7C,YAANqB,MAAM,GAAW;AAAE,iBAAO,KAAKC,GAAL,CAASvB,GAAhB;AAAsB;;AACnC,YAANsB,MAAM,CAACrB,KAAD,EAAgB;AAAE,eAAKsB,GAAL,CAASvB,GAAT,GAAeC,KAAf;AAAuB;;AAMtC,YAATuB,SAAS,GAAW;AAAE,iBAAO,KAAKC,MAAL,CAAYzB,GAAnB;AAAyB;;AACtC,YAATwB,SAAS,CAACvB,KAAD,EAAgB;AAAE,eAAKwB,MAAL,CAAYzB,GAAZ,GAAkBC,KAAlB;AAA0B;;AAQjD,eAARyB,QAAQ,CAACC,IAAD,EAAyB;AACpC,gBAAMC,IAAI,GAAG,IAAIpC,WAAJ,EAAb;;AACA,cAAImC,IAAJ,EAAU;AACNE,YAAAA,MAAM,CAACC,MAAP,CAAcF,IAAd,EAAoBD,IAApB;AACAC,YAAAA,IAAI,CAACG,cAAL,GAAsB,CAACJ,IAAI,CAACI,cAAL,IAAuB,EAAxB,EAA4BC,GAA5B,CAAgC;AAAA;AAAA,kDAAeN,QAA/C,CAAtB;AACH;;AAED,iBAAOE,IAAP;AACH;;AArHoB,O;;;;;iBAIM,I;;;;;;;iBAGM;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMO;AAAA;AAAA,kDAAoB,OAApB,C;;;;;;;iBAMlB,K;;;;;;;iBAEqB;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAOlB,I;;;;;;;iBAKN,I;;;;;;;iBAGqB;AAAA;AAAA,kDAAoB,KAApB,C;;;;;;;iBAMA;AAAA;AAAA,kDAAoB,MAApB,C;;;;;;;iBAOH;AAAA;AAAA,kDAAoB,KAApB,C;;;;;;;iBAOG;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAOG;AAAA;AAAA,kDAAoB,KAApB,C;;;;;;;iBAOD;AAAA;AAAA,kDAAoB,KAApB,C;;;;;;;iBAOT;AAAA;AAAA,kDAAoB,IAApB,C;;;;;;;iBAOA;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAOF;AAAA;AAAA,kDAAoB,IAApB,C;;;;;;;iBAOG;AAAA;AAAA,kDAAoB,KAApB,C;;;;;;;iBAMZ,I;;;;;;;iBAGa,E", "sourcesContent": ["\r\nimport { _decorator, error, v2, Vec2, Prefab } from \"cc\";\r\nimport { EventGroupData } from \"./EventGroupData\";\r\nimport { ExpressionValue } from \"./ExpressionValue\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * 发射器数据\r\n * 所有时间相关的，单位都是毫秒(ms)\r\n */\r\n@ccclass(\"EmitterData\")\r\nexport class EmitterData {\r\n    // @property({displayName: '发射器名称'})\r\n    // name : string = '';                // uid \r\n    @property({displayName: '是否仅在屏幕内发射', group: '基础属性'})\r\n    isOnlyInScreen : boolean = true;   // 仅在屏幕内才发射\r\n\r\n    @property({visible:false})\r\n    initialDelay : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '初始延迟', group: '基础属性'})\r\n    get initialDelayStr(): string { return this.initialDelay.raw; }\r\n    set initialDelayStr(value: string) { this.initialDelay.raw = value; }\r\n\r\n    @property({visible:false})\r\n    public emitDuration : ExpressionValue = new ExpressionValue('10000');\r\n    @property({displayName: '发射器持续时间', group: '基础属性'})\r\n    public get emitDurationStr(): string { return this.emitDuration.raw; }\r\n    public set emitDurationStr(value: string) { this.emitDuration.raw = value; }\r\n\r\n    @property({displayName: '是否预热', tooltip: '预热xxx', group: '基础属性'})\r\n    isPreWarm : boolean = false;       // 是否预热\r\n    @property({visible:false})\r\n    public preWarmDuration : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '预热持续时长', group: '基础属性'})\r\n    public get preWarmDurationStr(): string { return this.preWarmDuration.raw; }\r\n    public set preWarmDurationStr(value: string) { this.preWarmDuration.raw = value; }\r\n\r\n    // 预热特效:(这个是否用prefab，直接包含音效、音量等信息)\r\n    @property({type: Prefab, displayName: '预热特效', group: '基础属性'})\r\n    preWarmEffect : Prefab = null!; \r\n    // @property({displayName: '预热音效'})\r\n    // preWarmSound : string;             // 预热音效\r\n    \r\n    @property({displayName: '是否循环', group: '基础属性'})\r\n    isLoop : boolean = true;           // 是否循环\r\n    // 循环间隔\r\n    @property({visible:false})\r\n    public loopInterval : ExpressionValue = new ExpressionValue('0.0');\r\n    @property({displayName: '循环间隔', tooltip: \"发射器持续时间结束->循环间隔->预热\", group: '基础属性'})\r\n    public get loopIntervalStr(): string { return this.loopInterval.raw; }\r\n    public set loopIntervalStr(value: string) { this.loopInterval.raw = value; }\r\n\r\n    @property({visible:false})\r\n    public emitInterval : ExpressionValue = new ExpressionValue('1000');\r\n    @property({displayName: '开火间隔', group: '基础属性'})\r\n    public get emitIntervalStr(): string { return this.emitInterval.raw; }\r\n    public set emitIntervalStr(value: string) { this.emitInterval.raw = value; }\r\n\r\n    // 用来修改子弹初始速度的乘数(备用)\r\n    @property({visible:false})\r\n    public emitPower : ExpressionValue = new ExpressionValue('1.0');\r\n    @property({displayName: '发射速度', group: '基础属性'})\r\n    public get emitPowerStr(): string { return this.emitPower.raw; }\r\n    public set emitPowerStr(value: string) { this.emitPower.raw = value; }\r\n\r\n    // 单次发射数量\r\n    @property({visible:false})\r\n    public perEmitCount : ExpressionValue = new ExpressionValue('1');\r\n    @property({displayName: '单次发射数量', group: '基础属性'})\r\n    public get perEmitCountStr(): string { return this.perEmitCount.raw; }\r\n    public set perEmitCountStr(value: string) { this.perEmitCount.raw = value; }\r\n\r\n    // 单次发射多个子弹时的间隔\r\n    @property({visible:false})\r\n    public perEmitInterval : ExpressionValue = new ExpressionValue('0.0');\r\n    @property({displayName: '单次发射多个子弹时的间隔', group: '基础属性'})\r\n    public get perEmitIntervalStr(): string { return this.perEmitInterval.raw; }\r\n    public set perEmitIntervalStr(value: string) { this.perEmitInterval.raw = value; }\r\n     \r\n    // 单次发射多个子弹时的x偏移    \r\n    @property({visible:false})\r\n    public perEmitOffsetX : ExpressionValue = new ExpressionValue('0.0');\r\n    @property({displayName: '单次发射多个子弹时的x偏移', group: '基础属性'})\r\n    public get perEmitOffsetXStr(): string { return this.perEmitOffsetX.raw; }\r\n    public set perEmitOffsetXStr(value: string) { this.perEmitOffsetX.raw = value; }\r\n\r\n    // 发射方向: 0朝右, 90朝上, 180朝左, 270朝下(or -90)\r\n    @property({visible:false})\r\n    public angle : ExpressionValue = new ExpressionValue('90');\r\n    @property({displayName: '弹道角度', tooltip:\"决定发射方向\", group: '基础属性'})\r\n    public get angleStr(): string { return this.angle.raw; }\r\n    public set angleStr(value: string) { this.angle.raw = value; }\r\n\r\n    // 发射条数(弹道数量)\r\n    @property({visible:false})\r\n    public count : ExpressionValue = new ExpressionValue('1');\r\n    @property({displayName: '弹道数量', tooltip:\"决定发射条数\", group: '基础属性'})\r\n    public get countStr(): string { return this.count.raw; }\r\n    public set countStr(value: string) { this.count.raw = value; }\r\n\r\n    // 发射范围(弧度范围)\r\n    @property({visible:false})\r\n    public arc : ExpressionValue = new ExpressionValue('60');\r\n    @property({displayName: '发射范围', group: '基础属性'})\r\n    public get arcStr(): string { return this.arc.raw; }\r\n    public set arcStr(value: string) { this.arc.raw = value; }\r\n\r\n    // 发射半径\r\n    @property({visible:false})\r\n    public radius : ExpressionValue = new ExpressionValue('1.0');\r\n    @property({displayName: '发射半径', group: '基础属性'})\r\n    public get radiusStr(): string { return this.radius.raw; }\r\n    public set radiusStr(value: string) { this.radius.raw = value; }\r\n\r\n    @property({type:Prefab, displayName: '发射特效', group: '基础属性'})\r\n    emitEffect : Prefab = null!;               // 发射特效(多个的话建议做到prefab上?) 包含音效?\r\n\r\n    @property({type:[EventGroupData], displayName: '事件组', group: '事件组'})\r\n    eventGroupData: EventGroupData[] = [];\r\n\r\n    static fromJSON(json: any): EmitterData {\r\n        const data = new EmitterData();\r\n        if (json) {\r\n            Object.assign(data, json);\r\n            data.eventGroupData = (json.eventGroupData || []).map(EventGroupData.fromJSON);\r\n        }\r\n\r\n        return data;\r\n    }\r\n}"]}