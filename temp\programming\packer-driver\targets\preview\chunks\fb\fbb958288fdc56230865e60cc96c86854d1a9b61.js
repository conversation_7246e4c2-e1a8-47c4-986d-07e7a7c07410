System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Vec3, EventConditionBase, Comparer, eCompareOp, EmitterConditionBase, EmitterCondition_Active, EmitterCondition_InitialDelay, EmitterCondition_Prewarm, EmitterCondition_PrewarmDuration, EmitterCondition_Duration, EmitterCondition_ElapsedTime, EmitterCondition_Loop, EmitterCondition_LoopInterval, EmitterCondition_EmitInterval, EmitterCondition_PerEmitCount, EmitterCondition_PerEmitInterval, EmitterCondition_PerEmitOffsetX, EmitterCondition_Angle, EmitterCondition_Count, EmitterCondition_BulletDuration, EmitterCondition_BulletSpeed, EmitterCondition_BulletAcceleration, EmitterCondition_BulletAccelerationAngle, EmitterCondition_BulletFacingMoveDir, EmitterCondition_BulletTrackingTarget, EmitterCondition_BulletDestructive, EmitterCondition_BulletDestructiveOnHit, EmitterCondition_BulletHitEffect, EmitterCondition_BulletScale, EmitterCondition_BulletColorR, EmitterCondition_BulletColorG, EmitterCondition_BulletColorB, EmitterCondition_BulletDefaultFacing, EmitterCondition_PlayerActLevel, EmitterCondition_PlayerPosX, EmitterCondition_PlayerPosY, EmitterCondition_PlayerLifePercent, EmitterCondition_PlayerGainBuff, _crd;

  function _reportPossibleCrUseOfEventConditionBase(extras) {
    _reporterNs.report("EventConditionBase", "./IEventCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventGroupContext(extras) {
    _reporterNs.report("IEventGroupContext", "../EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfComparer(extras) {
    _reporterNs.report("Comparer", "../EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeCompareOp(extras) {
    _reporterNs.report("eCompareOp", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  _export({
    EmitterConditionBase: void 0,
    EmitterCondition_Active: void 0,
    EmitterCondition_InitialDelay: void 0,
    EmitterCondition_Prewarm: void 0,
    EmitterCondition_PrewarmDuration: void 0,
    EmitterCondition_Duration: void 0,
    EmitterCondition_ElapsedTime: void 0,
    EmitterCondition_Loop: void 0,
    EmitterCondition_LoopInterval: void 0,
    EmitterCondition_EmitInterval: void 0,
    EmitterCondition_PerEmitCount: void 0,
    EmitterCondition_PerEmitInterval: void 0,
    EmitterCondition_PerEmitOffsetX: void 0,
    EmitterCondition_Angle: void 0,
    EmitterCondition_Count: void 0,
    EmitterCondition_BulletDuration: void 0,
    EmitterCondition_BulletSpeed: void 0,
    EmitterCondition_BulletAcceleration: void 0,
    EmitterCondition_BulletAccelerationAngle: void 0,
    EmitterCondition_BulletFacingMoveDir: void 0,
    EmitterCondition_BulletTrackingTarget: void 0,
    EmitterCondition_BulletDestructive: void 0,
    EmitterCondition_BulletDestructiveOnHit: void 0,
    EmitterCondition_BulletHitEffect: void 0,
    EmitterCondition_BulletScale: void 0,
    EmitterCondition_BulletColorR: void 0,
    EmitterCondition_BulletColorG: void 0,
    EmitterCondition_BulletColorB: void 0,
    EmitterCondition_BulletDefaultFacing: void 0,
    EmitterCondition_PlayerActLevel: void 0,
    EmitterCondition_PlayerPosX: void 0,
    EmitterCondition_PlayerPosY: void 0,
    EmitterCondition_PlayerLifePercent: void 0,
    EmitterCondition_PlayerGainBuff: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      EventConditionBase = _unresolved_2.EventConditionBase;
    }, function (_unresolved_3) {
      Comparer = _unresolved_3.Comparer;
    }, function (_unresolved_4) {
      eCompareOp = _unresolved_4.eCompareOp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ddaaevzyOxHM4UOE7HRfbrs", "EmitterEventConditions", undefined);

      __checkObsolete__(['_decorator', 'Vec3']);

      _export("EmitterConditionBase", EmitterConditionBase = class EmitterConditionBase extends (_crd && EventConditionBase === void 0 ? (_reportPossibleCrUseOfEventConditionBase({
        error: Error()
      }), EventConditionBase) : EventConditionBase) {}); /////////////////////////////////////////////////////////////////////////////////
      // 以下是发射器相关参数
      /////////////////////////////////////////////////////////////////////////////////
      // 发射器是否启用


      _export("EmitterCondition_Active", EmitterCondition_Active = class EmitterCondition_Active extends EmitterConditionBase {
        evaluate(context) {
          // Custom evaluation logic for active condition
          return context.emitter.isActive.value;
        }

      }); // 发射器初始延迟时间


      _export("EmitterCondition_InitialDelay", EmitterCondition_InitialDelay = class EmitterCondition_InitialDelay extends EmitterConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.emitter.initialDelay.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_Prewarm", EmitterCondition_Prewarm = class EmitterCondition_Prewarm extends EmitterConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return context.emitter.isPreWarm.value === (this._targetValue === 1) ? true : false;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return context.emitter.isPreWarm.value !== (this._targetValue === 1) ? true : false;

            default:
              return false;
          }
        }

      });

      _export("EmitterCondition_PrewarmDuration", EmitterCondition_PrewarmDuration = class EmitterCondition_PrewarmDuration extends EmitterConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.emitter.preWarmDuration.value, this._targetValue, this.data.compareOp);
        }

      }); // 发射器持续时间


      _export("EmitterCondition_Duration", EmitterCondition_Duration = class EmitterCondition_Duration extends EmitterConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.emitter.emitDuration.value, this._targetValue, this.data.compareOp);
        }

      }); // 发射器已运行时间


      _export("EmitterCondition_ElapsedTime", EmitterCondition_ElapsedTime = class EmitterCondition_ElapsedTime extends EmitterConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.emitter.elapsedTime.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_Loop", EmitterCondition_Loop = class EmitterCondition_Loop extends EmitterConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return context.emitter.isLoop.value === (this._targetValue === 1) ? true : false;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return context.emitter.isLoop.value !== (this._targetValue === 1) ? true : false;

            default:
              return false;
          }
        }

      });

      _export("EmitterCondition_LoopInterval", EmitterCondition_LoopInterval = class EmitterCondition_LoopInterval extends EmitterConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.emitter.loopInterval.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_EmitInterval", EmitterCondition_EmitInterval = class EmitterCondition_EmitInterval extends EmitterConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.emitter.emitInterval.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_PerEmitCount", EmitterCondition_PerEmitCount = class EmitterCondition_PerEmitCount extends EmitterConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.emitter.perEmitCount.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_PerEmitInterval", EmitterCondition_PerEmitInterval = class EmitterCondition_PerEmitInterval extends EmitterConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.emitter.perEmitInterval.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_PerEmitOffsetX", EmitterCondition_PerEmitOffsetX = class EmitterCondition_PerEmitOffsetX extends EmitterConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.emitter.perEmitOffsetX.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_Angle", EmitterCondition_Angle = class EmitterCondition_Angle extends EmitterConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.emitter.angle.value, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_Count", EmitterCondition_Count = class EmitterCondition_Count extends EmitterConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.emitter.count.value, this._targetValue, this.data.compareOp);
        }

      }); /////////////////////////////////////////////////////////////////////////////////
      // 以下是发射器配置的子弹相关参数
      /////////////////////////////////////////////////////////////////////////////////


      _export("EmitterCondition_BulletDuration", EmitterCondition_BulletDuration = class EmitterCondition_BulletDuration extends EmitterConditionBase {
        constructor() {
          super(...arguments);
          this._evalValue = 0;
        }

        onLoad(context) {
          super.onLoad(context);
          this._evalValue = context.emitter.bulletProp.duration.value;
        }

        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(this._evalValue, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_BulletSpeed", EmitterCondition_BulletSpeed = class EmitterCondition_BulletSpeed extends EmitterConditionBase {
        constructor() {
          super(...arguments);
          this._evalValue = 0;
        }

        onLoad(context) {
          super.onLoad(context);
          this._evalValue = context.emitter.bulletProp.speed.value;
        }

        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(this._evalValue, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_BulletAcceleration", EmitterCondition_BulletAcceleration = class EmitterCondition_BulletAcceleration extends EmitterConditionBase {
        constructor() {
          super(...arguments);
          this._evalValue = 0;
        }

        onLoad(context) {
          super.onLoad(context);
          this._evalValue = context.emitter.bulletProp.acceleration.value;
        }

        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(this._evalValue, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_BulletAccelerationAngle", EmitterCondition_BulletAccelerationAngle = class EmitterCondition_BulletAccelerationAngle extends EmitterConditionBase {
        constructor() {
          super(...arguments);
          this._evalValue = 0;
        }

        onLoad(context) {
          super.onLoad(context);
          this._evalValue = context.emitter.bulletProp.accelerationAngle.value;
        }

        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(this._evalValue, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_BulletFacingMoveDir", EmitterCondition_BulletFacingMoveDir = class EmitterCondition_BulletFacingMoveDir extends EmitterConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return context.emitter.bulletProp.isFacingMoveDir.value === (this._targetValue === 1) ? true : false;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return context.emitter.bulletProp.isFacingMoveDir.value !== (this._targetValue === 1) ? true : false;

            default:
              return false;
          }
        }

      });

      _export("EmitterCondition_BulletTrackingTarget", EmitterCondition_BulletTrackingTarget = class EmitterCondition_BulletTrackingTarget extends EmitterConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return context.emitter.bulletProp.isTrackingTarget.value === (this._targetValue === 1) ? true : false;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return context.emitter.bulletProp.isTrackingTarget.value !== (this._targetValue === 1) ? true : false;

            default:
              return false;
          }
        }

      });

      _export("EmitterCondition_BulletDestructive", EmitterCondition_BulletDestructive = class EmitterCondition_BulletDestructive extends EmitterConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return context.emitter.bulletProp.isDestructive.value === (this._targetValue === 1) ? true : false;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return context.emitter.bulletProp.isDestructive.value !== (this._targetValue === 1) ? true : false;

            default:
              return false;
          }
        }

      });

      _export("EmitterCondition_BulletDestructiveOnHit", EmitterCondition_BulletDestructiveOnHit = class EmitterCondition_BulletDestructiveOnHit extends EmitterConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return context.emitter.bulletProp.isDestructiveOnHit.value === (this._targetValue === 1) ? true : false;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return context.emitter.bulletProp.isDestructiveOnHit.value !== (this._targetValue === 1) ? true : false;

            default:
              return false;
          }
        }

      });

      _export("EmitterCondition_BulletHitEffect", EmitterCondition_BulletHitEffect = class EmitterCondition_BulletHitEffect extends EmitterConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal: // return context.emitter!.bulletData.hitEffect === (this._targetValue === 1) ? true : false;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual: // return context.emitter!.bulletData.hitEffect !== (this._targetValue === 1) ? true : false;

            default:
              return false;
          }
        }

      });

      _export("EmitterCondition_BulletScale", EmitterCondition_BulletScale = class EmitterCondition_BulletScale extends EmitterConditionBase {
        constructor() {
          super(...arguments);
          this._evalValue = 1;
        }

        onLoad(context) {
          super.onLoad(context);
          this._evalValue = context.emitter.bulletProp.scale.value;
        }

        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(this._evalValue, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_BulletColorR", EmitterCondition_BulletColorR = class EmitterCondition_BulletColorR extends EmitterConditionBase {
        constructor() {
          super(...arguments);
          this._evalValue = 0;
        }

        onLoad(context) {
          super.onLoad(context);
          this._evalValue = context.emitter.bulletProp.color.value.r;
        }

        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(this._evalValue, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_BulletColorG", EmitterCondition_BulletColorG = class EmitterCondition_BulletColorG extends EmitterConditionBase {
        constructor() {
          super(...arguments);
          this._evalValue = 0;
        }

        onLoad(context) {
          super.onLoad(context);
          this._evalValue = context.emitter.bulletProp.color.value.g;
        }

        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(this._evalValue, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_BulletColorB", EmitterCondition_BulletColorB = class EmitterCondition_BulletColorB extends EmitterConditionBase {
        constructor() {
          super(...arguments);
          this._evalValue = 0;
        }

        onLoad(context) {
          super.onLoad(context);
          this._evalValue = context.emitter.bulletProp.color.value.b;
        }

        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(this._evalValue, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_BulletDefaultFacing", EmitterCondition_BulletDefaultFacing = class EmitterCondition_BulletDefaultFacing extends EmitterConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return context.emitter.bulletProp.defaultFacing.value === this._targetValue;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return context.emitter.bulletProp.defaultFacing.value !== this._targetValue;

            default:
              return false;
          }
        }

      }); /////////////////////////////////////////////////////////////////////////////////
      // Player
      /////////////////////////////////////////////////////////////////////////////////
      // 玩家account等级


      _export("EmitterCondition_PlayerActLevel", EmitterCondition_PlayerActLevel = class EmitterCondition_PlayerActLevel extends EmitterConditionBase {// TODO:
      });

      _export("EmitterCondition_PlayerPosX", EmitterCondition_PlayerPosX = class EmitterCondition_PlayerPosX extends EmitterConditionBase {
        constructor() {
          super(...arguments);
          this._playerPos = Vec3.ZERO;
        }

        evaluate(context) {
          if (context.playerPlane === null) {
            return false;
          }

          context.playerPlane.node.getPosition(this._playerPos);
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(this._playerPos.x, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_PlayerPosY", EmitterCondition_PlayerPosY = class EmitterCondition_PlayerPosY extends EmitterCondition_PlayerPosX {
        evaluate(context) {
          if (context.playerPlane === null) {
            return false;
          }

          context.playerPlane.node.getPosition(this._playerPos);
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(this._playerPos.y, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_PlayerLifePercent", EmitterCondition_PlayerLifePercent = class EmitterCondition_PlayerLifePercent extends EmitterConditionBase {
        evaluate(context) {
          if (context.playerPlane === null) {
            return false;
          }

          var hp_ratio = context.playerPlane.curHp / context.playerPlane.maxHp * 100;
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(hp_ratio, this._targetValue, this.data.compareOp);
        }

      });

      _export("EmitterCondition_PlayerGainBuff", EmitterCondition_PlayerGainBuff = class EmitterCondition_PlayerGainBuff extends EmitterConditionBase {
        evaluate(context) {
          var _context$playerPlane$;

          if (context.playerPlane === null) {
            return false;
          }

          return (_context$playerPlane$ = context.playerPlane.buffComp) == null ? void 0 : _context$playerPlane$.HasBuff(this._targetValue);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fbb958288fdc56230865e60cc96c86854d1a9b61.js.map