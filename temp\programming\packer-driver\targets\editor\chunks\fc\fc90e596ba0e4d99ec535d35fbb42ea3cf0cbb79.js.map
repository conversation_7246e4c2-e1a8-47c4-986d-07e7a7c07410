{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave.ts"], "names": ["LevelDataEventWaveGroup", "LevelDataEventTriggerWave", "instantiate", "MyApp", "Wave", "GameIns", "LevelDataEventTrigger", "LevelDataEventTriggerType", "waveUUID", "weight", "constructor", "waveGroup", "_selectedWaveGroup", "onInit", "length", "totalWeight", "for<PERSON>ach", "randomWeight", "Math", "floor", "random", "curWeight", "onTrigger", "x", "y", "path", "resMgr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultBundleName", "load", "err", "prefab", "console", "error", "waveComp", "getComponent", "waveManager", "addWaveByLevel", "fromJSON", "obj"], "mappings": ";;;oKAMaA,uB,EAKAC,yB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAXQC,MAAAA,W,OAAAA,W;;AACZC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,qB,iBAAAA,qB;AAAuBC,MAAAA,yB,iBAAAA,yB;;;;;;;;;yCAEnBP,uB,GAAN,MAAMA,uBAAN,CAA8B;AAAA;AAAA,eAC1BQ,QAD0B,GACL,EADK;AAAA,eAE1BC,MAF0B,GAET,EAFS;AAAA;;AAAA,O;;2CAKxBR,yB,GAAN,MAAMA,yBAAN;AAAA;AAAA,0DAA8D;AAIjES,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sEAA0BN,IAAhC;AADU,eAHPO,SAGO,GAHgC,EAGhC;AAAA,eAFNC,kBAEM,GAF+C,IAE/C;AAEb;;AAEMC,QAAAA,MAAM,GAAG;AACZ;AACA,cAAI,KAAKF,SAAL,CAAeG,MAAf,GAAwB,CAA5B,EAA+B;AAC3B,gBAAIC,WAAW,GAAG,CAAlB;AACA,iBAAKJ,SAAL,CAAeK,OAAf,CAAuBL,SAAS,IAAI;AAChCI,cAAAA,WAAW,IAAIJ,SAAS,CAACF,MAAzB;AACH,aAFD;AAIA,gBAAIQ,YAAY,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBL,WAA3B,CAAnB;AACA,gBAAIM,SAAS,GAAG,CAAhB;;AAEA,iBAAK,IAAIV,SAAT,IAAsB,KAAKA,SAA3B,EAAsC;AAClCU,cAAAA,SAAS,IAAIV,SAAS,CAACF,MAAvB;;AACA,kBAAIQ,YAAY,IAAII,SAApB,EAA+B;AAC3B,qBAAKT,kBAAL,GAA0BD,SAA1B;AACA;AACH;AACJ;AACJ;AACJ;;AAEMW,QAAAA,SAAS,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AACnC,eAAKZ,kBAAL,CAAyBJ,QAAzB,CAAkCQ,OAAlC,CAA2CR,QAAD,IAAc;AACpD,kBAAMiB,IAAI,GAAG;AAAA;AAAA,gCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,gCAAMD,MAAN,CAAaE,iBAAvC,EAA0DpB,QAA1D,CAAb;AACA;AAAA;AAAA,gCAAMkB,MAAN,CAAaG,IAAb,CAAkBJ,IAAlB,EAAwB,CAACK,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,kBAAID,GAAJ,EAAS;AACLE,gBAAAA,OAAO,CAACC,KAAR,CAAc,2BAAd,EAA2C,8BAA3C,EAA2EH,GAA3E;AACA;AACH;;AACD,oBAAMI,QAAQ,GAAGhC,WAAW,CAAC6B,MAAD,CAAX,CAAoBI,YAApB;AAAA;AAAA,+BAAjB;AACA;AAAA;AAAA,sCAAQC,WAAR,CAAoBC,cAApB,CAAmCH,QAAnC,EAA8CX,CAA9C,EAAiDC,CAAjD;AACH,aAPD;AAQH,WAVD;AAWH;;AAEMc,QAAAA,QAAQ,CAACC,GAAD,EAAiB;AAC5B,gBAAMD,QAAN,CAAeC,GAAf;AACH;;AA7CgE,O", "sourcesContent": ["import { _decorator, instantiate, Prefab } from \"cc\";\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { Wave } from \"../../game/wave/Wave\";\r\nimport { GameIns } from \"../../game/GameIns\";\r\nimport { LevelDataEventTrigger, LevelDataEventTriggerType } from \"./LevelDataEventTrigger\";\r\n\r\nexport class LevelDataEventWaveGroup {\r\n    public waveUUID: string[] = [];\r\n    public weight: number = 50;\r\n}\r\n\r\nexport class LevelDataEventTriggerWave extends LevelDataEventTrigger {\r\n    public waveGroup: LevelDataEventWaveGroup[] = [];\r\n    private _selectedWaveGroup: LevelDataEventWaveGroup | null = null;\r\n    \r\n    constructor() {\r\n        super(LevelDataEventTriggerType.Wave);\r\n    }\r\n    \r\n    public onInit() {\r\n        // 提前创建好wave，但不执行\r\n        if (this.waveGroup.length > 0) {\r\n            let totalWeight = 0;\r\n            this.waveGroup.forEach(waveGroup => {\r\n                totalWeight += waveGroup.weight;\r\n            });\r\n\r\n            let randomWeight = Math.floor(Math.random() * totalWeight);\r\n            let curWeight = 0;\r\n            \r\n            for (let waveGroup of this.waveGroup) {\r\n                curWeight += waveGroup.weight;\r\n                if (randomWeight <= curWeight) {\r\n                    this._selectedWaveGroup = waveGroup;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    public onTrigger(x: number, y: number) {\r\n        this._selectedWaveGroup!.waveUUID.forEach((waveUUID) => {\r\n            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, waveUUID)\r\n            MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                if (err) {\r\n                    console.error('LevelDataEventTriggerWave', \" onInit load wave prefab err\", err);\r\n                    return;\r\n                }\r\n                const waveComp = instantiate(prefab).getComponent(Wave);\r\n                GameIns.waveManager.addWaveByLevel(waveComp!, x, y);\r\n            });\r\n        });\r\n    }\r\n\r\n    public fromJSON(obj: any): void {\r\n        super.fromJSON(obj);\r\n    }\r\n}\r\n\r\n"]}