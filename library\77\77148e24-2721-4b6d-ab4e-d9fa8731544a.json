[{"__type__": "cc.Prefab", "_name": "Bullet_211100301", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Bullet_211100301", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}], "_prefab": {"__id__": 36}, "_lpos": {"__type__": "cc.Vec3", "x": -10.187, "y": 251.125, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "begKFdA65LzYkynT0HbkIl"}, {"__type__": "2564dArcRFKZKoo3odCQrHw", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "bulletID": 0, "bulletPrefab": null, "emitterData": {"__id__": 6}, "bulletData": {"__id__": 28}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31k/54u8dPRZsJ/5MHRwps"}, {"__type__": "EmitterData", "isOnlyInScreen": true, "initialDelay": {"__id__": 7}, "emitDuration": {"__id__": 8}, "isPreWarm": false, "preWarmDuration": {"__id__": 9}, "preWarmEffect": null, "isLoop": true, "loopInterval": {"__id__": 10}, "emitInterval": {"__id__": 11}, "emitPower": {"__id__": 12}, "perEmitCount": {"__id__": 13}, "perEmitInterval": {"__id__": 14}, "perEmitOffsetX": {"__id__": 15}, "angle": {"__id__": 16}, "count": {"__id__": 17}, "arc": {"__id__": 18}, "radius": {"__id__": 19}, "emitEffect": null, "eventGroupData": [{"__id__": 20}]}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 3000, "isExpression": false, "expression": "3000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 2000, "isExpression": false, "expression": "2000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 100, "isExpression": false, "expression": "100", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "EventGroupData", "name": "角度旋转0-180", "triggerCount": 1, "conditions": [{"__id__": 21}], "actions": [{"__id__": 23}]}, {"__type__": "EventConditionData", "op": 0, "type": 6, "compareOp": 4, "targetValue": {"__id__": 22}}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "EventActionData", "name": "", "type": 13, "delay": {"__id__": 24}, "duration": {"__id__": 25}, "targetValueType": 1, "targetValue": {"__id__": 26}, "transitionDuration": {"__id__": 27}, "wrapMode": 0, "easing": 0}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 3000, "isExpression": false, "expression": "3000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 180, "isExpression": false, "expression": "180", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 3000, "isExpression": false, "expression": "3000", "serializedProgram": null}, {"__type__": "BulletData", "isFacingMoveDir": true, "isTrackingTarget": false, "isDestroyOutScreen": true, "isDestructive": false, "isDestructiveOnHit": false, "scale": {"__id__": 29}, "duration": {"__id__": 30}, "delayDestroy": {"__id__": 31}, "speed": {"__id__": 32}, "acceleration": {"__id__": 33}, "accelerationAngle": {"__id__": 34}, "eventGroupData": [{"__id__": 35}]}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 5000, "isExpression": false, "expression": "5000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 600, "isExpression": false, "expression": "600", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "EventGroupData", "name": "", "triggerCount": 1, "conditions": [], "actions": []}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f6gcZ/lXZAt4gDq6zMfn2j", "targetOverrides": null}]