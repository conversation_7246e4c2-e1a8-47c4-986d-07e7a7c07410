{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/event/EventGroupCom.ts"], "names": ["ComContext", "EventGroupComp", "BaseComp", "emitter", "bullet", "<PERSON><PERSON><PERSON>", "plane", "reset", "_context", "init", "entity", "updateGameLogic", "dt"], "mappings": ";;;wCAKaA,U,EAgBAC,c;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AApBNC,MAAAA,Q;;;;;;;4BAIMF,U,GAAN,MAAMA,UAAN,CAA+C;AAAA;AAAA,eAClDG,OADkD,GAClC,IADkC;AAAA,eAElDC,MAFkD,GAEnC,IAFmC;AAAA,eAGlDC,WAHkD,GAG9B,IAH8B;AAAA,eAKlDC,KALkD,GAKrB,IALqB;AAAA;;AAOlDC,QAAAA,KAAK,GAAG;AACJ,eAAKJ,OAAL,GAAe,IAAf;AACA,eAAKC,MAAL,GAAc,IAAd;AACA,eAAKC,WAAL,GAAmB,IAAnB;AACA,eAAKC,KAAL,GAAa,IAAb;AACH;;AAZiD,O,GAetD;;;gCACaL,c,GAAN,MAAMA,cAAN;AAAA;AAAA,gCAAsC;AAAA;AAAA;AAAA,eACzCK,KADyC,GACZ,IADY;AAAA,eAGjCE,QAHiC,GAGV,IAAIR,UAAJ,EAHU;AAAA;;AAKzCS,QAAAA,IAAI,CAACC,MAAD,EAAiB;AACjB,gBAAMD,IAAN,CAAWC,MAAX;AACA,eAAKJ,KAAL,GAAaI,MAAb;AACA,eAAKF,QAAL,CAAcF,KAAd,GAAsB,KAAKA,KAA3B;AACH;;AAEDK,QAAAA,eAAe,CAACC,EAAD,EAAa,CAE3B;;AAbwC,O", "sourcesContent": ["import Entity from \"db://assets/bundles/common/script/game/ui/base/Entity\";\r\nimport BaseComp from \"db://assets/bundles/common/script/game/ui/base/BaseComp\";\r\nimport EnemyPlaneBase from \"db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase\";\r\nimport { IEventGroupContext } from \"../../../bullet/EventGroup\";\r\n\r\nexport class ComContext implements IEventGroupContext {\r\n    emitter: null = null;\r\n    bullet: null = null;\r\n    playerPlane: null = null;\r\n\r\n    plane: EnemyPlaneBase|null = null;\r\n\r\n    reset() {\r\n        this.emitter = null;\r\n        this.bullet = null;\r\n        this.playerPlane = null;\r\n        this.plane = null;\r\n    }\r\n}\r\n\r\n// 挂在Entity身上，用来执行事件组的组件\r\nexport class EventGroupComp extends BaseComp {\r\n    plane: EnemyPlaneBase|null = null;\r\n\r\n    private _context: ComContext = new ComContext();\r\n\r\n    init(entity: Entity) {\r\n        super.init(entity);\r\n        this.plane = entity as EnemyPlaneBase;\r\n        this._context.plane = this.plane;\r\n    }\r\n\r\n    updateGameLogic(dt: number) {\r\n\r\n    }\r\n}"]}