{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameRuleManager.ts"], "names": ["GameRuleManager", "director", "SingletonBase", "GameEnum", "GameIns", "_gameResult", "_gameState", "GameState", "Idle", "reset", "gameIdle", "setGameState", "gameSortie", "isInBattle", "<PERSON><PERSON><PERSON>", "gameStart", "Battle", "gamePause", "Pause", "gameResume", "resume", "gameWillOver", "WillOver", "gameOver", "Over", "updateGameLogic", "dt", "mainPlaneManager", "mainPlane", "isDead", "battleManager", "gameType", "GameType", "Gold", "enemyManager", "isEnemyOver", "enemyOver", "boss<PERSON><PERSON><PERSON>", "isBossOver", "battleSucc", "_isGameState", "isGameWillOver", "isGameOver", "getGameResult", "state", "gameState"], "mappings": ";;;2HAKaA,e;;;;;;;;;;;;;;;;;;;;;;;AALJC,MAAAA,Q,OAAAA,Q;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;iCAEIJ,e,GAAN,MAAMA,eAAN;AAAA;AAAA,0CAA6D;AAAA;AAAA;AAAA,eAEhEK,WAFgE,GAElD,KAFkD;AAAA,eAGhEC,UAHgE,GAGnD;AAAA;AAAA,oCAASC,SAAT,CAAmBC,IAHgC;AAAA;;AAKhE;AACJ;AACA;AACIC,QAAAA,KAAK,GAAG;AACJ,eAAKJ,WAAL,GAAmB,KAAnB;AACA,eAAKC,UAAL,GAAkB;AAAA;AAAA,oCAASC,SAAT,CAAmBC,IAArC;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,QAAQ,GAAG;AACP,eAAKC,YAAL,CAAkB;AAAA;AAAA,oCAASJ,SAAT,CAAmBC,IAArC;AACH;AAED;AACJ;AACA;;;AACII,QAAAA,UAAU,GAAG;AACT,cAAI,CAAC,KAAKC,UAAL,EAAL,EAAwB;AACpB,iBAAKF,YAAL,CAAkB;AAAA;AAAA,sCAASJ,SAAT,CAAmBO,MAArC;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,SAAS,GAAG;AACR,eAAKJ,YAAL,CAAkB;AAAA;AAAA,oCAASJ,SAAT,CAAmBS,MAArC;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,SAAS,GAAG;AACR,eAAKN,YAAL,CAAkB;AAAA;AAAA,oCAASJ,SAAT,CAAmBW,KAArC;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT,eAAKR,YAAL,CAAkB;AAAA;AAAA,oCAASJ,SAAT,CAAmBS,MAArC;AACAf,UAAAA,QAAQ,CAACmB,MAAT;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,YAAY,GAAG;AACX,eAAKV,YAAL,CAAkB;AAAA;AAAA,oCAASJ,SAAT,CAAmBe,QAArC;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,QAAQ,GAAG;AACP,eAAKZ,YAAL,CAAkB;AAAA;AAAA,oCAASJ,SAAT,CAAmBiB,IAArC;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACC,EAAD,EAAa;AAAA;;AACxB,cAAI,KAAKrB,WAAL,6BAAoB;AAAA;AAAA,kCAAQsB,gBAAR,CAAyBC,SAA7C,aAAoB,sBAAoCC,MAA5D,EAAoE;AAChE;AACH;;AAED,cAAI;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,QAAtB,KAAmC;AAAA;AAAA,oCAASC,QAAT,CAAkBC,IAAzD,EAA+D;AAC3D,gBAAI;AAAA;AAAA,oCAAQC,YAAR,CAAqBC,WAArB,EAAJ,EAAwC;AACpC,mBAAK9B,WAAL,GAAmB,IAAnB;AACH;AACJ,WAJD,MAIO;AACH;AACA;AACA,kBAAM+B,SAAS,GAAG;AAAA;AAAA,oCAAQF,YAAR,CAAqBC,WAArB,EAAlB;;AAEA,gBAAIC,SAAS,IAAI;AAAA;AAAA,oCAAQC,WAAR,CAAoBC,UAApB,EAAjB,EAAmD;AAC/C,mBAAKjC,WAAL,GAAmB,IAAnB;AACA;AAAA;AAAA,sCAAQyB,aAAR,CAAsBS,UAAtB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACI1B,QAAAA,UAAU,GAAG;AACT,iBAAO,KAAK2B,YAAL,CAAkB;AAAA;AAAA,oCAASjC,SAAT,CAAmBS,MAArC,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIyB,QAAAA,cAAc,GAAG;AACb,iBAAO,KAAKD,YAAL,CAAkB;AAAA;AAAA,oCAASjC,SAAT,CAAmBe,QAArC,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIoB,QAAAA,UAAU,GAAG;AACT,iBAAO,KAAKF,YAAL,CAAkB;AAAA;AAAA,oCAASjC,SAAT,CAAmBiB,IAArC,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACImB,QAAAA,aAAa,GAAG;AACZ,iBAAO,KAAKtC,WAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIM,QAAAA,YAAY,CAACiC,KAAD,EAAgB;AACxB,eAAKtC,UAAL,GAAkBsC,KAAlB;AACH;AAED;AACJ;AACA;;;AACiB,YAATC,SAAS,GAAG;AACZ,iBAAO,KAAKvC,UAAZ;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIkC,QAAAA,YAAY,CAACI,KAAD,EAAgB;AACxB,iBAAO,KAAKtC,UAAL,KAAoBsC,KAA3B;AACH;;AAhJ+D,O", "sourcesContent": ["import { director } from \"cc\";\r\nimport { SingletonBase } from \"../../../../../scripts/core/base/SingletonBase\";\r\nimport { GameEnum } from \"../const/GameEnum\";\r\nimport { GameIns } from \"../GameIns\";\r\n\r\nexport class GameRuleManager extends SingletonBase<GameRuleManager> {\r\n\r\n    _gameResult = false;\r\n    _gameState = GameEnum.GameState.Idle;\r\n\r\n    /**\r\n     * 重置游戏规则\r\n     */\r\n    reset() {\r\n        this._gameResult = false;\r\n        this._gameState = GameEnum.GameState.Idle;\r\n    }\r\n\r\n    /**\r\n     * 设置游戏为空闲状态\r\n     */\r\n    gameIdle() {\r\n        this.setGameState(GameEnum.GameState.Idle);\r\n    }\r\n\r\n    /**\r\n     * 设置游戏为出击状态\r\n     */\r\n    gameSortie() {\r\n        if (!this.isInBattle()) {\r\n            this.setGameState(GameEnum.GameState.Sortie);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始游戏\r\n     */\r\n    gameStart() {\r\n        this.setGameState(GameEnum.GameState.Battle);\r\n    }\r\n\r\n    /**\r\n     * 暂停游戏\r\n     */\r\n    gamePause() {\r\n        this.setGameState(GameEnum.GameState.Pause);\r\n    }\r\n\r\n    /**\r\n     * 恢复游戏\r\n     */\r\n    gameResume() {\r\n        this.setGameState(GameEnum.GameState.Battle);\r\n        director.resume();\r\n    }\r\n\r\n    /**\r\n     * 设置游戏为即将结束状态\r\n     */\r\n    gameWillOver() {\r\n        this.setGameState(GameEnum.GameState.WillOver);\r\n    }\r\n\r\n    /**\r\n     * 设置游戏为结束状态\r\n     */\r\n    gameOver() {\r\n        this.setGameState(GameEnum.GameState.Over);\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} dt 每帧的时间间隔\r\n     */\r\n    updateGameLogic(dt: number) {\r\n        if (this._gameResult || GameIns.mainPlaneManager.mainPlane?.isDead) {\r\n            return;\r\n        }\r\n\r\n        if (GameIns.battleManager.gameType === GameEnum.GameType.Gold) {\r\n            if (GameIns.enemyManager.isEnemyOver()) {\r\n                this._gameResult = true;\r\n            }\r\n        } else {\r\n            // 因为wave是动态创建的，这里先不通过这个来了，可以走事件来触发\r\n            // const waveOver = GameIns.waveManager.isEnemyOver();\r\n            const enemyOver = GameIns.enemyManager.isEnemyOver();\r\n\r\n            if (enemyOver && GameIns.bossManager.isBossOver()) {\r\n                this._gameResult = true;\r\n                GameIns.battleManager.battleSucc();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 判断游戏是否处于战斗状态\r\n     * @returns {boolean} 是否处于战斗状态\r\n     */\r\n    isInBattle() {\r\n        return this._isGameState(GameEnum.GameState.Battle);\r\n    }\r\n\r\n    /**\r\n     * 判断游戏是否即将结束\r\n     * @returns {boolean} 是否即将结束\r\n     */\r\n    isGameWillOver() {\r\n        return this._isGameState(GameEnum.GameState.WillOver);\r\n    }\r\n\r\n    /**\r\n     * 判断游戏是否结束\r\n     * @returns {boolean} 是否结束\r\n     */\r\n    isGameOver() {\r\n        return this._isGameState(GameEnum.GameState.Over);\r\n    }\r\n\r\n    /**\r\n     * 获取游戏结果\r\n     * @returns {boolean} 游戏结果\r\n     */\r\n    getGameResult() {\r\n        return this._gameResult;\r\n    }\r\n\r\n    /**\r\n     * 设置游戏状态\r\n     * @param {GameEnum.GameState} state 游戏状态\r\n     */\r\n    setGameState(state: number) {\r\n        this._gameState = state;\r\n    }\r\n\r\n    /**\r\n     * 获取游戏状态\r\n     */\r\n    get gameState() {\r\n        return this._gameState;\r\n    }\r\n\r\n    /**\r\n     * 判断当前游戏状态是否为指定状态\r\n     * @param {GameEnum.GameState} state 游戏状态\r\n     * @returns {boolean} 是否为指定状态\r\n     */\r\n    _isGameState(state: number) {\r\n        return this._gameState === state;\r\n    }\r\n}"]}