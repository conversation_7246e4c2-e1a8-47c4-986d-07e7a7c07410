System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, director, SingletonBase, GameEnum, GameIns, GameRuleManager, _crd;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../../../../scripts/core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  _export("GameRuleManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      director = _cc.director;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameEnum = _unresolved_3.GameEnum;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "443d8umFW9N9IBmV3oMVJTh", "GameRuleManager", undefined);

      __checkObsolete__(['director']);

      _export("GameRuleManager", GameRuleManager = class GameRuleManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor(...args) {
          super(...args);
          this._gameResult = false;
          this._gameState = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Idle;
        }

        /**
         * 重置游戏规则
         */
        reset() {
          this._gameResult = false;
          this._gameState = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Idle;
        }
        /**
         * 设置游戏为空闲状态
         */


        gameIdle() {
          this.setGameState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Idle);
        }
        /**
         * 设置游戏为出击状态
         */


        gameSortie() {
          if (!this.isInBattle()) {
            this.setGameState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).GameState.Sortie);
          }
        }
        /**
         * 开始游戏
         */


        gameStart() {
          this.setGameState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle);
        }
        /**
         * 暂停游戏
         */


        gamePause() {
          this.setGameState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Pause);
        }
        /**
         * 恢复游戏
         */


        gameResume() {
          this.setGameState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle);
          director.resume();
        }
        /**
         * 设置游戏为即将结束状态
         */


        gameWillOver() {
          this.setGameState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.WillOver);
        }
        /**
         * 设置游戏为结束状态
         */


        gameOver() {
          this.setGameState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Over);
        }
        /**
         * 更新游戏逻辑
         * @param {number} dt 每帧的时间间隔
         */


        updateGameLogic(dt) {
          var _mainPlaneManager$mai;

          if (this._gameResult || (_mainPlaneManager$mai = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane) != null && _mainPlaneManager$mai.isDead) {
            return;
          }

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.gameType === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameType.Gold) {
            if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.isEnemyOver()) {
              this._gameResult = true;
            }
          } else {
            // 因为wave是动态创建的，这里先不通过这个来了，可以走事件来触发
            // const waveOver = GameIns.waveManager.isEnemyOver();
            const enemyOver = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.isEnemyOver();

            if (enemyOver && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bossManager.isBossOver()) {
              this._gameResult = true;
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.battleSucc();
            }
          }
        }
        /**
         * 判断游戏是否处于战斗状态
         * @returns {boolean} 是否处于战斗状态
         */


        isInBattle() {
          return this._isGameState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle);
        }
        /**
         * 判断游戏是否即将结束
         * @returns {boolean} 是否即将结束
         */


        isGameWillOver() {
          return this._isGameState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.WillOver);
        }
        /**
         * 判断游戏是否结束
         * @returns {boolean} 是否结束
         */


        isGameOver() {
          return this._isGameState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Over);
        }
        /**
         * 获取游戏结果
         * @returns {boolean} 游戏结果
         */


        getGameResult() {
          return this._gameResult;
        }
        /**
         * 设置游戏状态
         * @param {GameEnum.GameState} state 游戏状态
         */


        setGameState(state) {
          this._gameState = state;
        }
        /**
         * 获取游戏状态
         */


        get gameState() {
          return this._gameState;
        }
        /**
         * 判断当前游戏状态是否为指定状态
         * @param {GameEnum.GameState} state 游戏状态
         * @returns {boolean} 是否为指定状态
         */


        _isGameState(state) {
          return this._gameState === state;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=feeed2e4534582f63c6e383401f2c5d3048e1914.js.map