System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, instantiate, Node, view, MyApp, LevelDataEventTriggerType, _dec, _class, _crd, ccclass, TerrainsNodeName, DynamicNodeName, WaveNodeName, EventNodeName, LevelLayerUI;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEvent(extras) {
    _reporterNs.report("LevelDataEvent", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerType(extras) {
    _reporterNs.report("LevelDataEventTriggerType", "../../../leveldata/trigger/LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerLog(extras) {
    _reporterNs.report("LevelDataEventTriggerLog", "../../../leveldata/trigger/LevelDataEventTriggerLog", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerWave(extras) {
    _reporterNs.report("LevelDataEventTriggerWave", "../../../leveldata/trigger/LevelDataEventTriggerWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtion(extras) {
    _reporterNs.report("LevelDataEventCondtion", "../../../leveldata/condition/LevelDataEventCondtion", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Node = _cc.Node;
      view = _cc.view;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      LevelDataEventTriggerType = _unresolved_3.LevelDataEventTriggerType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d139a2/Z9NEzIGUXE+qqxb8", "LevelLayerUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'instantiate', 'Node', 'Prefab', 'view']);

      ({
        ccclass
      } = _decorator);
      TerrainsNodeName = "terrains";
      DynamicNodeName = "dynamic";
      WaveNodeName = "waves";
      EventNodeName = "events";

      _export("LevelLayerUI", LevelLayerUI = (_dec = ccclass('LevelLayerUI'), _dec(_class = class LevelLayerUI extends Component {
        constructor() {
          super(...arguments);
          this.backgrounds = [];
          this._offSetY = 0;
          // 当前关卡的偏移量
          this._bTrackBackground = true;
          // 是否跟随背景移动（预加载关卡未在显示区域的时候跟随）
          this.terrainsNode = null;
          this.dynamicNode = null;
          // private waves: LevelDataWave[] = [];
          this.events = [];
          this.enableEvents = [];
        }

        get TrackBackground() {
          return this._bTrackBackground;
        }

        set TrackBackground(value) {
          this._bTrackBackground = value;
        }

        onLoad() {}

        initByLevelData(data, offSetY) {
          var _data$events, _data$terrains;

          this._offSetY = offSetY;
          this.node.setPosition(0, offSetY, 0);
          this.terrainsNode = this._getOrAddNode(this.node, TerrainsNodeName);
          this.dynamicNode = this._getOrAddNode(this.node, DynamicNodeName);
          console.log('LevelLayerUI', " initByLevelData", "events:" + ((_data$events = data.events) == null ? void 0 : _data$events.length));
          this.backgrounds = [];
          (_data$terrains = data.terrains) == null || _data$terrains.forEach(terrain => {
            var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.uuid);
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
              if (err) {
                console.error('LevelLayerUI', " initByLevelData load terrain prefab err", err);
                return;
              }

              var terrainNode = instantiate(prefab);
              terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
              terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
              terrainNode.setRotationFromEuler(0, 0, terrain.rotation);
              this.terrainsNode.addChild(terrainNode);
            });
          }); // this.waves = [...data.waves]
          // this.waves.sort((a, b) => a.position.y - b.position.y);

          this.events = [...data.events];
          this.events.sort((a, b) => a.position.y - b.position.y);
          this.events.forEach(event => {
            event.triggers.forEach(trigger => {
              trigger.onInit();
            });
          });
        }

        tick(deltaTime, speed) {
          if (this.TrackBackground === true) {
            var posY = this.node.getPosition().y;
            var topPosY = view.getVisibleSize().height / 2;

            if (posY < topPosY) {
              this._bTrackBackground = false;
            }
          }

          var prePosY = this.node.getPosition().y;
          this.node.setPosition(0, prePosY - deltaTime * speed, 0); // while (this.waves.length > 0 && this.waves[0].position.y < this.node.getPosition().y) {
          // const wave = this.waves[0];
          // this.waves.shift();
          // const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, wave.waveUUID)
          // MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {
          //     if (err) {
          //         console.error('LevelLayerUI', " tick load wave prefab err", err);
          //         return;
          //     }
          //     const waveComp = instantiate(prefab).getComponent(Wave)
          //     GameIns.waveManager.addWaveByLevel(waveComp!, wave.position.x, wave.position.y - this.node.position.y);
          // });
          // }

          while (this.events.length > 0 && this.events[0].position.y < this.node.getPosition().y) {
            var event = this.events[0];
            this.events.shift();
            this.enableEvents.push(event);
          }

          for (var i = this.enableEvents.length - 1; i >= 0; i--) {
            var _event = this.enableEvents[i];
            var condResult = this.evalConditions(_event.conditions);

            if (condResult) {
              this.enableEvents.splice(i, 1);
              this.execActions(_event);
            }
          }
        }

        _getOrAddNode(node_parent, name) {
          var node = node_parent.getChildByName(name);

          if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
          }

          return node;
        }

        evalConditions(conditions) {
          var result = true;

          for (var cond of conditions) {
            switch (cond._type) {}
          }

          return result;
        }

        execActions(event) {
          for (var trigger of event.triggers) {
            switch (trigger._type) {
              case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                error: Error()
              }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Log:
                console.log("LevelLayerUI", "trigger log", trigger.message);
                break;

              case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                error: Error()
              }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Audio:
                break;

              case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                error: Error()
              }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave:
                console.log("LevelLayerUI", "trigger wave");
                var waveTrigger = trigger;

                if (!waveTrigger.waveGroup || waveTrigger.waveGroup.length == 0) {
                  break;
                }

                waveTrigger.onTrigger(event.position.x, Math.max(0, event.position.y - this.node.position.y));
                break;

              default:
                break;
            }
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=928ab71a9883ce6ff25bdd946042179d19325cc0.js.map