System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, BaseComp, ComContext, EventGroupComp, _crd;

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "db://assets/bundles/common/script/game/ui/base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseComp(extras) {
    _reporterNs.report("BaseComp", "db://assets/bundles/common/script/game/ui/base/BaseComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlaneBase(extras) {
    _reporterNs.report("EnemyPlaneBase", "db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventGroupContext(extras) {
    _reporterNs.report("IEventGroupContext", "../../../bullet/EventGroup", _context.meta, extras);
  }

  _export({
    ComContext: void 0,
    EventGroupComp: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      BaseComp = _unresolved_2.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e0954V0gBVLYI+UJyy+iwWi", "EventGroupCom", undefined);

      _export("ComContext", ComContext = class ComContext {
        constructor() {
          this.emitter = null;
          this.bullet = null;
          this.playerPlane = null;
          this.plane = null;
        }

        reset() {
          this.emitter = null;
          this.bullet = null;
          this.playerPlane = null;
          this.plane = null;
        }

      }); // 挂在Entity身上，用来执行事件组的组件


      _export("EventGroupComp", EventGroupComp = class EventGroupComp extends (_crd && BaseComp === void 0 ? (_reportPossibleCrUseOfBaseComp({
        error: Error()
      }), BaseComp) : BaseComp) {
        constructor() {
          super(...arguments);
          this.plane = null;
          this._context = new ComContext();
        }

        init(entity) {
          super.init(entity);
          this.plane = entity;
          this._context.plane = this.plane;
        }

        updateGameLogic(dt) {}

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6e8372a14d821670c1f6f6f048c69a8d03d444e7.js.map