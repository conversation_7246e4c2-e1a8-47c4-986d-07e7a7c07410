{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelLayerUI.ts"], "names": ["_decorator", "Component", "instantiate", "Node", "view", "MyApp", "LevelDataEventTriggerType", "ccclass", "TerrainsNodeName", "DynamicNodeName", "WaveNodeName", "EventNodeName", "LevelLayerUI", "backgrounds", "_offSetY", "_bTrackBackground", "terrainsNode", "dynamicNode", "events", "enableEvents", "TrackBackground", "value", "onLoad", "initByLevelData", "data", "offSetY", "node", "setPosition", "_getOrAddNode", "console", "log", "length", "terrains", "for<PERSON>ach", "terrain", "path", "resMgr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultBundleName", "uuid", "load", "err", "prefab", "error", "terrainNode", "position", "x", "y", "setScale", "scale", "setRotationFromEuler", "rotation", "<PERSON><PERSON><PERSON><PERSON>", "sort", "a", "b", "event", "triggers", "trigger", "onInit", "tick", "deltaTime", "speed", "posY", "getPosition", "topPosY", "getVisibleSize", "height", "prePosY", "shift", "push", "i", "condResult", "evalConditions", "conditions", "splice", "execActions", "node_parent", "name", "getChildByName", "result", "cond", "_type", "Log", "message", "Audio", "Wave", "waveTrigger", "waveGroup", "onTrigger", "Math", "max"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,I,OAAAA,I;;AAClDC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,yB,iBAAAA,yB;;;;;;;;;OAOH;AAAEC,QAAAA;AAAF,O,GAAcP,U;AAEdQ,MAAAA,gB,GAAmB,U;AACnBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,Y,GAAe,O;AACfC,MAAAA,a,GAAgB,Q;;8BAGTC,Y,WADZL,OAAO,CAAC,cAAD,C,gBAAR,MACaK,YADb,SACkCX,SADlC,CAC4C;AAAA;AAAA;AAAA,eACjCY,WADiC,GACT,EADS;AAAA,eAEhCC,QAFgC,GAEb,CAFa;AAEV;AAFU,eAGhCC,iBAHgC,GAGH,IAHG;AAGG;AAHH,eAKhCC,YALgC,GAKJ,IALI;AAAA,eAMhCC,WANgC,GAML,IANK;AAOxC;AAPwC,eAQhCC,MARgC,GAQL,EARK;AAAA,eAShCC,YATgC,GASC,EATD;AAAA;;AAWd,YAAfC,eAAe,GAAY;AAClC,iBAAO,KAAKL,iBAAZ;AACH;;AACyB,YAAfK,eAAe,CAACC,KAAD,EAAiB;AACvC,eAAKN,iBAAL,GAAyBM,KAAzB;AACH;;AAEDC,QAAAA,MAAM,GAAS,CAEd;;AAEMC,QAAAA,eAAe,CAACC,IAAD,EAAuBC,OAAvB,EAA8C;AAAA;;AAChE,eAAKX,QAAL,GAAgBW,OAAhB;AACA,eAAKC,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBF,OAAzB,EAAkC,CAAlC;AAEA,eAAKT,YAAL,GAAoB,KAAKY,aAAL,CAAmB,KAAKF,IAAxB,EAA8BlB,gBAA9B,CAApB;AACA,eAAKS,WAAL,GAAmB,KAAKW,aAAL,CAAmB,KAAKF,IAAxB,EAA8BjB,eAA9B,CAAnB;AAEAoB,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4B,kBAA5B,EAAiD,UAAD,gBAAUN,IAAI,CAACN,MAAf,qBAAU,aAAaa,MAAO,EAA9E;AACA,eAAKlB,WAAL,GAAmB,EAAnB;AAEA,4BAAAW,IAAI,CAACQ,QAAL,4BAAeC,OAAf,CAAwBC,OAAD,IAAa;AAChC,kBAAMC,IAAI,GAAG;AAAA;AAAA,gCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,gCAAMD,MAAN,CAAaE,iBAAvC,EAA0DJ,OAAO,CAACK,IAAlE,CAAb;AACA;AAAA;AAAA,gCAAMH,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACM,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,kBAAID,GAAJ,EAAS;AACLZ,gBAAAA,OAAO,CAACc,KAAR,CAAc,cAAd,EAA8B,0CAA9B,EAA0EF,GAA1E;AACA;AACH;;AACD,kBAAIG,WAAW,GAAG1C,WAAW,CAACwC,MAAD,CAA7B;AACAE,cAAAA,WAAW,CAACjB,WAAZ,CAAwBO,OAAO,CAACW,QAAR,CAAiBC,CAAzC,EAA4CZ,OAAO,CAACW,QAAR,CAAiBE,CAA7D,EAAgE,CAAhE;AACAH,cAAAA,WAAW,CAACI,QAAZ,CAAqBd,OAAO,CAACe,KAAR,CAAcH,CAAnC,EAAsCZ,OAAO,CAACe,KAAR,CAAcF,CAApD,EAAuD,CAAvD;AACAH,cAAAA,WAAW,CAACM,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuChB,OAAO,CAACiB,QAA/C;AACA,mBAAKnC,YAAL,CAAmBoC,QAAnB,CAA4BR,WAA5B;AACH,aAVD;AAWH,WAbD,EAVgE,CAwBhE;AACA;;AACA,eAAK1B,MAAL,GAAc,CAAC,GAAGM,IAAI,CAACN,MAAT,CAAd;AACA,eAAKA,MAAL,CAAYmC,IAAZ,CAAiB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACT,QAAF,CAAWE,CAAX,GAAeQ,CAAC,CAACV,QAAF,CAAWE,CAArD;AACA,eAAK7B,MAAL,CAAYe,OAAZ,CAAqBuB,KAAD,IAAW;AAC3BA,YAAAA,KAAK,CAACC,QAAN,CAAexB,OAAf,CAAwByB,OAAD,IAAa;AAChCA,cAAAA,OAAO,CAACC,MAAR;AACH,aAFD;AAGH,WAJD;AAKH;;AAEMC,QAAAA,IAAI,CAACC,SAAD,EAAoBC,KAApB,EAAyC;AAChD,cAAI,KAAK1C,eAAL,KAAyB,IAA7B,EAAmC;AAC/B,kBAAM2C,IAAI,GAAG,KAAKrC,IAAL,CAAUsC,WAAV,GAAwBjB,CAArC;AACA,kBAAMkB,OAAO,GAAG7D,IAAI,CAAC8D,cAAL,GAAsBC,MAAtB,GAA+B,CAA/C;;AACA,gBAAIJ,IAAI,GAAGE,OAAX,EAAoB;AAChB,mBAAKlD,iBAAL,GAAyB,KAAzB;AACH;AACJ;;AACD,gBAAMqD,OAAO,GAAG,KAAK1C,IAAL,CAAUsC,WAAV,GAAwBjB,CAAxC;AACA,eAAKrB,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyByC,OAAO,GAAGP,SAAS,GAAGC,KAA/C,EAAsD,CAAtD,EATgD,CAUhD;AACI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACJ;;AACA,iBAAO,KAAK5C,MAAL,CAAYa,MAAZ,GAAqB,CAArB,IAA0B,KAAKb,MAAL,CAAY,CAAZ,EAAe2B,QAAf,CAAwBE,CAAxB,GAA4B,KAAKrB,IAAL,CAAUsC,WAAV,GAAwBjB,CAArF,EAAwF;AACpF,kBAAMS,KAAK,GAAG,KAAKtC,MAAL,CAAY,CAAZ,CAAd;AACA,iBAAKA,MAAL,CAAYmD,KAAZ;AACA,iBAAKlD,YAAL,CAAkBmD,IAAlB,CAAuBd,KAAvB;AACH;;AACD,eAAK,IAAIe,CAAC,GAAG,KAAKpD,YAAL,CAAkBY,MAAlB,GAA2B,CAAxC,EAA2CwC,CAAC,IAAI,CAAhD,EAAmDA,CAAC,EAApD,EAAwD;AACpD,kBAAMf,KAAK,GAAG,KAAKrC,YAAL,CAAkBoD,CAAlB,CAAd;AACA,gBAAIC,UAAU,GAAG,KAAKC,cAAL,CAAoBjB,KAAK,CAACkB,UAA1B,CAAjB;;AAEA,gBAAIF,UAAJ,EAAgB;AACZ,mBAAKrD,YAAL,CAAkBwD,MAAlB,CAAyBJ,CAAzB,EAA4B,CAA5B;AACA,mBAAKK,WAAL,CAAiBpB,KAAjB;AACH;AACJ;AACJ;;AAEO5B,QAAAA,aAAa,CAACiD,WAAD,EAAoBC,IAApB,EAAwC;AACzD,cAAIpD,IAAI,GAAGmD,WAAW,CAACE,cAAZ,CAA2BD,IAA3B,CAAX;;AACA,cAAIpD,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAIvB,IAAJ,CAAS2E,IAAT,CAAP;AACAD,YAAAA,WAAW,CAACzB,QAAZ,CAAqB1B,IAArB;AACH;;AACD,iBAAOA,IAAP;AACH;;AAED+C,QAAAA,cAAc,CAACC,UAAD,EAAgD;AAC1D,cAAIM,MAAM,GAAG,IAAb;;AACA,eAAK,IAAIC,IAAT,IAAiBP,UAAjB,EAA6B;AACzB,oBAAQO,IAAI,CAACC,KAAb;AAEH;;AACD,iBAAOF,MAAP;AACH;;AAEDJ,QAAAA,WAAW,CAACpB,KAAD,EAA8B;AACrC,eAAK,IAAIE,OAAT,IAAoBF,KAAK,CAACC,QAA1B,EAAoC;AAChC,oBAAQC,OAAO,CAACwB,KAAhB;AACI,mBAAK;AAAA;AAAA,0EAA0BC,GAA/B;AACItD,gBAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4B,aAA5B,EAA4C4B,OAAD,CAAsC0B,OAAjF;AACA;;AACJ,mBAAK;AAAA;AAAA,0EAA0BC,KAA/B;AACI;;AACJ,mBAAK;AAAA;AAAA,0EAA0BC,IAA/B;AACIzD,gBAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4B,cAA5B;AACA,sBAAMyD,WAAW,GAAG7B,OAApB;;AACA,oBAAI,CAAC6B,WAAW,CAACC,SAAb,IAA0BD,WAAW,CAACC,SAAZ,CAAsBzD,MAAtB,IAAgC,CAA9D,EAAiE;AAC7D;AACH;;AAEDwD,gBAAAA,WAAW,CAACE,SAAZ,CAAsBjC,KAAK,CAACX,QAAN,CAAeC,CAArC,EAAwC4C,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYnC,KAAK,CAACX,QAAN,CAAeE,CAAf,GAAmB,KAAKrB,IAAL,CAAUmB,QAAV,CAAmBE,CAAlD,CAAxC;AACA;;AACJ;AACI;AAhBR;AAkBH;AACJ;;AAvIuC,O", "sourcesContent": ["import { _decorator, Component, instantiate, Node, Prefab, view } from \"cc\";\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { LevelDataEvent, LevelDataLayer, LevelDataWave } from \"../../../leveldata/leveldata\";\r\nimport { LevelDataEventTriggerType } from \"../../../leveldata/trigger/LevelDataEventTrigger\";\r\nimport { LevelDataEventTriggerLog } from \"../../../leveldata/trigger/LevelDataEventTriggerLog\";\r\nimport { LevelDataEventTriggerWave, LevelDataEventWaveGroup } from \"../../../leveldata/trigger/LevelDataEventTriggerWave\";\r\nimport { GameIns } from \"../../GameIns\";\r\nimport { Wave } from \"../../wave/Wave\";\r\nimport { LevelDataEventCondtion } from \"../../../leveldata/condition/LevelDataEventCondtion\";\r\n\r\nconst { ccclass } = _decorator;\r\n\r\nconst TerrainsNodeName = \"terrains\";\r\nconst DynamicNodeName = \"dynamic\";\r\nconst WaveNodeName = \"waves\";\r\nconst EventNodeName = \"events\"\r\n\r\n@ccclass('LevelLayerUI')\r\nexport class LevelLayerUI extends Component {\r\n    public backgrounds: Prefab[] = [];\r\n    private _offSetY: number = 0; // 当前关卡的偏移量\r\n    private _bTrackBackground: boolean = true; // 是否跟随背景移动（预加载关卡未在显示区域的时候跟随）\r\n\r\n    private terrainsNode: Node | null = null;\r\n    private dynamicNode: Node | null = null;\r\n    // private waves: LevelDataWave[] = [];\r\n    private events: LevelDataEvent[] = [];\r\n    private enableEvents: LevelDataEvent[] = [];\r\n\r\n    public get TrackBackground(): boolean {\r\n        return this._bTrackBackground;\r\n    }\r\n    public set TrackBackground(value: boolean) {\r\n        this._bTrackBackground = value;\r\n    }\r\n\r\n    onLoad(): void {\r\n\r\n    }\r\n\r\n    public initByLevelData(data: LevelDataLayer, offSetY: number): void {\r\n        this._offSetY = offSetY;\r\n        this.node.setPosition(0, offSetY, 0);\r\n\r\n        this.terrainsNode = this._getOrAddNode(this.node, TerrainsNodeName);\r\n        this.dynamicNode = this._getOrAddNode(this.node, DynamicNodeName);\r\n\r\n        console.log('LevelLayerUI', \" initByLevelData\", `events:${data.events?.length}`);\r\n        this.backgrounds = [];\r\n\r\n        data.terrains?.forEach((terrain) => {\r\n            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid)\r\n            MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                if (err) {\r\n                    console.error('LevelLayerUI', \" initByLevelData load terrain prefab err\", err);\r\n                    return;\r\n                }\r\n                var terrainNode = instantiate(prefab);\r\n                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);\r\n                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);\r\n                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);\r\n                this.terrainsNode!.addChild(terrainNode);\r\n            });\r\n        });\r\n        // this.waves = [...data.waves]\r\n        // this.waves.sort((a, b) => a.position.y - b.position.y);\r\n        this.events = [...data.events]\r\n        this.events.sort((a, b) => a.position.y - b.position.y);\r\n        this.events.forEach((event) => {\r\n            event.triggers.forEach((trigger) => {\r\n                trigger.onInit();\r\n            })\r\n        });\r\n    }\r\n\r\n    public tick(deltaTime: number, speed: number): void {\r\n        if (this.TrackBackground === true) {\r\n            const posY = this.node.getPosition().y;\r\n            const topPosY = view.getVisibleSize().height / 2;\r\n            if (posY < topPosY) {\r\n                this._bTrackBackground = false;\r\n            }\r\n        }\r\n        const prePosY = this.node.getPosition().y;\r\n        this.node.setPosition(0, prePosY - deltaTime * speed, 0);\r\n        // while (this.waves.length > 0 && this.waves[0].position.y < this.node.getPosition().y) {\r\n            // const wave = this.waves[0];\r\n            // this.waves.shift();\r\n            // const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, wave.waveUUID)\r\n            // MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n            //     if (err) {\r\n            //         console.error('LevelLayerUI', \" tick load wave prefab err\", err);\r\n            //         return;\r\n            //     }\r\n            //     const waveComp = instantiate(prefab).getComponent(Wave)\r\n            //     GameIns.waveManager.addWaveByLevel(waveComp!, wave.position.x, wave.position.y - this.node.position.y);\r\n            // });\r\n        // }\r\n        while (this.events.length > 0 && this.events[0].position.y < this.node.getPosition().y) {\r\n            const event = this.events[0];\r\n            this.events.shift();\r\n            this.enableEvents.push(event);\r\n        }\r\n        for (let i = this.enableEvents.length - 1; i >= 0; i--) {\r\n            const event = this.enableEvents[i];\r\n            let condResult = this.evalConditions(event.conditions);\r\n\r\n            if (condResult) {\r\n                this.enableEvents.splice(i, 1);\r\n                this.execActions(event);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _getOrAddNode(node_parent: Node, name: string): Node {\r\n        var node = node_parent.getChildByName(name);\r\n        if (node == null) {\r\n            node = new Node(name);\r\n            node_parent.addChild(node);\r\n        }\r\n        return node;\r\n    }\r\n\r\n    evalConditions(conditions: LevelDataEventCondtion[]): boolean {\r\n        let result = true;\r\n        for (let cond of conditions) {\r\n            switch (cond._type) {\r\n            }\r\n        }\r\n        return result;\r\n    }\r\n\r\n    execActions(event: LevelDataEvent): void {\r\n        for (let trigger of event.triggers) {\r\n            switch (trigger._type) {\r\n                case LevelDataEventTriggerType.Log:\r\n                    console.log(\"LevelLayerUI\", \"trigger log\", (trigger as LevelDataEventTriggerLog).message);\r\n                    break;\r\n                case LevelDataEventTriggerType.Audio:\r\n                    break;\r\n                case LevelDataEventTriggerType.Wave:\r\n                    console.log(\"LevelLayerUI\", \"trigger wave\");\r\n                    const waveTrigger = trigger as LevelDataEventTriggerWave;\r\n                    if (!waveTrigger.waveGroup || waveTrigger.waveGroup.length == 0) {\r\n                        break;\r\n                    }\r\n                    \r\n                    waveTrigger.onTrigger(event.position.x, Math.max(0, event.position.y - this.node.position.y));\r\n                    break;\r\n                default:\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n}"]}