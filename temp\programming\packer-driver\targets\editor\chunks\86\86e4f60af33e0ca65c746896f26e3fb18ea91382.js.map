{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts"], "names": ["_decorator", "assetManager", "misc", "Prefab", "EDITOR", "MyApp", "BulletData", "EmitterData", "Bullet", "BulletProperty", "BulletSystem", "EventGroupContext", "ObjectPool", "PropertyContainerComponent", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "degreesToRadians", "radiansToDegrees", "eEmitterStatus", "eEmitterProp", "ePropMask", "Emitter", "displayName", "type", "editor<PERSON><PERSON><PERSON>", "onBulletCreatedCallback", "onEmitterStatusChangedCallback", "isActive", "isOnlyInScreen", "isPreWarm", "isLoop", "initialDelay", "preWarmDuration", "emitBulletID", "emitDuration", "emitInterval", "emitPower", "loopInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "arc", "radius", "elapsedTime", "bulletProp", "eventGroups", "_status", "None", "_statusElapsedTime", "_totalElapsedTime", "_isEmitting", "_nextEmitTime", "_bulletPrefab", "_prewarmEffectPrefab", "_emitEffectPrefab", "_entity", "_bulletConfig", "undefined", "_perEmitBulletQueue", "isEmitting", "status", "statusElapsedTime", "totalElapsedTime", "bulletConfig", "onLoad", "createProperties", "createEventGroups", "resetProperties", "onLostFocusInEditor", "updatePropertiesInEditor", "emitterData", "value", "bulletID", "eval", "notifyAll", "setIsActive", "active", "notify", "reset", "changeStatus", "length", "for<PERSON>ach", "group", "setEntity", "entity", "getEntity", "clear", "addProperty", "IsActive", "ElapsedTime", "EmitBulletID", "IsOnlyInScreen", "IsPreWarm", "IsLoop", "InitialDelay", "PrewarmDuration", "EmitDuration", "EmitInterval", "EmitPower", "LoopInterval", "PerEmitCount", "PerEmitInterval", "PerEmitOffsetX", "<PERSON><PERSON>", "Count", "Arc", "<PERSON><PERSON>", "on", "GetInstance", "lubanTables", "TbResBullet", "get", "resMgr", "load", "prefab", "error", "console", "onCreateEmitter", "onDestroyEmitter", "eventGroupData", "ctx", "emitter", "<PERSON><PERSON><PERSON>", "eventGroup", "createEmitterEventGroup", "resetFromData", "bulletData", "evalProperty", "prop", "canWrite", "ReEval", "oldStatus", "Prewarm", "tryStop", "tryStart", "scheduleNextEmit", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "j", "targetTime", "i", "push", "index", "perEmitIndex", "emitSingle", "processPerEmitQueue", "nextBullet", "shift", "tryEmit", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "radian", "x", "Math", "cos", "y", "sin", "getEmitOffsetX", "interval", "stepsFromMiddle", "floor", "ceil", "perpendicular", "bulletPrefab", "createBulletInEditor", "bullet", "instantiateBullet", "onCreateBullet", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "speedAngle", "atan2", "speed", "onReady", "prefabPath", "Editor", "Message", "request", "then", "uuid", "loadAny", "err", "bulletNode", "getNode", "bulletParent", "getComponent", "destroy", "name", "kBulletNameInEditor", "playEffect", "rotation", "duration", "effectNode", "setWorldRotation", "scheduleOnce", "returnNode", "isInScreen", "tick", "deltaTime", "updateStatusNone", "updateStatusPrewarm", "Emitting", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted", "wasEmitting"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AAChCC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,K,iBAAAA,K;;AAGAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,c,iBAAAA,c;;AACRC,MAAAA,Y,iBAAAA,Y;;AACYC,MAAAA,iB,iBAAAA,iB;;AACZC,MAAAA,U,iBAAAA,U;;AACUC,MAAAA,0B,iBAAAA,0B;;;;;;;;;OAIb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA;AAA1D,O,GAAmElB,U;OACnE;AAAEmB,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyClB,I;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;gCACYmB,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;cAIZ;;;8BACYC,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;AAQZ;AACA;AACA;AACA;AACA;AACA;;;2BACYC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;;AAQZ;AACA;AACA;AACA;AACA;yBAMaC,O,WALZV,OAAO,CAAC,SAAD,C,UAEPI,IAAI,CAAC,UAAD,C,UACJH,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAKZD,QAAQ,CAAC;AAAES,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRT,QAAQ,CAAC;AAAEU,QAAAA,IAAI,EAAEvB,MAAR;AAAgBsB,QAAAA,WAAW,EAAE,iBAA7B;AAAgDE,QAAAA,UAAU,EAAE;AAA5D,OAAD,C,UAGRX,QAAQ,CAAC;AAAEU,QAAAA,IAAI;AAAA;AAAA,sCAAN;AAAqBD,QAAAA,WAAW,EAAE;AAAlC,OAAD,C,UAGRT,QAAQ,CAAC;AAAEU,QAAAA,IAAI;AAAA;AAAA,oCAAN;AAAoBD,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,mFAlBb,MAKaD,OALb;AAAA;AAAA,oEAKsE;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAgBlE;AAhBkE,eAiBlEI,uBAjBkE,GAiBR,IAjBQ;AAAA,eAkBlEC,8BAlBkE,GAkBM,IAlBN;AAoBlE;AApBkE,eAqB3DC,QArB2D;AAAA,eAsB3DC,cAtB2D;AAAA,eAuB3DC,SAvB2D;AAAA,eAwB3DC,MAxB2D;AAAA,eAyB3DC,YAzB2D;AAAA,eA0B3DC,eA1B2D;AAAA,eA2B3DC,YA3B2D;AAAA,eA4B3DC,YA5B2D;AAAA,eA6B3DC,YA7B2D;AAAA,eA8B3DC,SA9B2D;AAAA,eA+B3DC,YA/B2D;AAAA,eAgC3DC,YAhC2D;AAAA,eAiC3DC,eAjC2D;AAAA,eAkC3DC,cAlC2D;AAAA,eAmC3DC,KAnC2D;AAAA,eAoC3DC,KApC2D;AAAA,eAqC3DC,GArC2D;AAAA,eAsC3DC,MAtC2D;AAAA,eAuC3DC,WAvC2D;AAwClE;AAxCkE,eAyC3DC,UAzC2D;AA2ClE;AA3CkE,eA4C3DC,WA5C2D,GA4C/B,EA5C+B;AA8ClE;AA9CkE,eA+CxDC,OA/CwD,GA+C9B9B,cAAc,CAAC+B,IA/Ce;AAAA,eAgDxDC,kBAhDwD,GAgD3B,CAhD2B;AAAA,eAiDxDC,iBAjDwD,GAiD5B,CAjD4B;AAAA,eAkDxDC,WAlDwD,GAkDjC,KAlDiC;AAAA,eAmDxDC,aAnDwD,GAmDhC,CAnDgC;AAAA,eAoDxDC,aApDwD,GAoDzB,IApDyB;AAAA,eAqDxDC,oBArDwD,GAqDlB,IArDkB;AAAA,eAsDxDC,iBAtDwD,GAsDrB,IAtDqB;AAAA,eAuDxDC,OAvDwD,GAuD5B,IAvD4B;AAAA,eAwDxDC,aAxDwD,GAwDjBC,SAxDiB;AA0DlE;AA1DkE,eA2DxDC,mBA3DwD,GA2DkC,EA3DlC;AAAA;;AA6DpD,YAAVC,UAAU,GAAY;AAAE,iBAAO,KAAKT,WAAZ;AAA0B;;AAC5C,YAANU,MAAM,GAAmB;AAAE,iBAAO,KAAKd,OAAZ;AAAsB;;AAChC,YAAjBe,iBAAiB,GAAW;AAAE,iBAAO,KAAKb,kBAAZ;AAAiC;;AAC/C,YAAhBc,gBAAgB,GAAW;AAAE,iBAAO,KAAKb,iBAAZ;AAAgC;;AACjD,YAAZc,YAAY,GAA0B;AAAE,iBAAO,KAAKP,aAAZ;AAA4B;;AAE9DQ,QAAAA,MAAM,GAAS;AACrB,eAAKC,gBAAL;AACA,eAAKC,iBAAL,GAFqB,CAIrB;;AACA,eAAKC,eAAL;AACH,SAzEiE,CA2ElE;;;AACOC,QAAAA,mBAAmB,GAAS;AAC/B,eAAKC,wBAAL;AACA,eAAKH,iBAAL;AACH;;AAEMG,QAAAA,wBAAwB,GAAG;AAC9B,cAAI,CAAC,KAAKC,WAAV,EAAuB;AAEvB,eAAK7C,QAAL,CAAc8C,KAAd,GAAsB,IAAtB;AACA,eAAKxC,YAAL,CAAkBwC,KAAlB,GAA0B,KAAKC,QAA/B;AACA,eAAK9C,cAAL,CAAoB6C,KAApB,GAA4B,KAAKD,WAAL,CAAiB5C,cAA7C;AACA,eAAKC,SAAL,CAAe4C,KAAf,GAAuB,KAAKD,WAAL,CAAiB3C,SAAxC;AACA,eAAKC,MAAL,CAAY2C,KAAZ,GAAoB,KAAKD,WAAL,CAAiB1C,MAArC;AAEA,eAAKC,YAAL,CAAkB0C,KAAlB,GAA0B,KAAKD,WAAL,CAAiBzC,YAAjB,CAA8B4C,IAA9B,EAA1B;AACA,eAAK3C,eAAL,CAAqByC,KAArB,GAA6B,KAAKD,WAAL,CAAiBxC,eAAjB,CAAiC2C,IAAjC,EAA7B;AACA,eAAKzC,YAAL,CAAkBuC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBtC,YAAjB,CAA8ByC,IAA9B,EAA1B;AACA,eAAKxC,YAAL,CAAkBsC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBrC,YAAjB,CAA8BwC,IAA9B,EAA1B;AACA,eAAKvC,SAAL,CAAeqC,KAAf,GAAuB,KAAKD,WAAL,CAAiBpC,SAAjB,CAA2BuC,IAA3B,EAAvB;AACA,eAAKtC,YAAL,CAAkBoC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBnC,YAAjB,CAA8BsC,IAA9B,EAA1B;AACA,eAAKrC,YAAL,CAAkBmC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBlC,YAAjB,CAA8BqC,IAA9B,EAA1B;AACA,eAAKpC,eAAL,CAAqBkC,KAArB,GAA6B,KAAKD,WAAL,CAAiBjC,eAAjB,CAAiCoC,IAAjC,EAA7B;AACA,eAAKnC,cAAL,CAAoBiC,KAApB,GAA4B,KAAKD,WAAL,CAAiBhC,cAAjB,CAAgCmC,IAAhC,EAA5B;AACA,eAAKlC,KAAL,CAAWgC,KAAX,GAAmB,KAAKD,WAAL,CAAiB/B,KAAjB,CAAuBkC,IAAvB,EAAnB;AACA,eAAKjC,KAAL,CAAW+B,KAAX,GAAmB,KAAKD,WAAL,CAAiB9B,KAAjB,CAAuBiC,IAAvB,EAAnB;AACA,eAAKhC,GAAL,CAAS8B,KAAT,GAAiB,KAAKD,WAAL,CAAiB7B,GAAjB,CAAqBgC,IAArB,EAAjB;AACA,eAAK/B,MAAL,CAAY6B,KAAZ,GAAoB,KAAKD,WAAL,CAAiB5B,MAAjB,CAAwB+B,IAAxB,EAApB;AAEA,eAAKC,SAAL,CAAe,IAAf;AACH,SAzGiE,CA0GlE;AAEA;;;AACOC,QAAAA,WAAW,CAACC,MAAD,EAAkB;AAChC,eAAKnD,QAAL,CAAc8C,KAAd,GAAsBK,MAAtB;AACA,eAAKnD,QAAL,CAAcoD,MAAd;AACH,SAhHiE,CAkHlE;;;AACOC,QAAAA,KAAK,GAAG;AACX,eAAK5B,WAAL,GAAmB,KAAnB;AACA,eAAK6B,YAAL,CAAkB/D,cAAc,CAAC+B,IAAjC;AACA,eAAKoB,eAAL;;AACA,cAAI,KAAKtB,WAAL,CAAiBmC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,iBAAKnC,WAAL,CAAiBoC,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACJ,KAAN,EAAlC;AACH;AACJ;;AAEMK,QAAAA,SAAS,CAACC,MAAD,EAAoB;AAChC,eAAK7B,OAAL,GAAe6B,MAAf;AACH;;AAEMC,QAAAA,SAAS,GAAqB;AACjC,iBAAO,KAAK9B,OAAZ;AACH;;AAESU,QAAAA,gBAAgB,GAAG;AACzB,eAAKqB,KAAL;AAEA,eAAK7D,QAAL,GAAgB,KAAK8D,WAAL,CAAiBtE,YAAY,CAACuE,QAA9B,EAAwC,KAAxC,CAAhB;AACA,eAAK7C,WAAL,GAAmB,KAAK4C,WAAL,CAAiBtE,YAAY,CAACwE,WAA9B,EAA2C,CAA3C,CAAnB;AACA,eAAK1D,YAAL,GAAoB,KAAKwD,WAAL,CAAiBtE,YAAY,CAACyE,YAA9B,EAA4C,KAAKlB,QAAjD,CAApB;AACA,eAAK9C,cAAL,GAAsB,KAAK6D,WAAL,CAAiBtE,YAAY,CAAC0E,cAA9B,EAA8C,IAA9C,CAAtB;AACA,eAAKhE,SAAL,GAAiB,KAAK4D,WAAL,CAAiBtE,YAAY,CAAC2E,SAA9B,EAAyC,IAAzC,CAAjB;AACA,eAAKhE,MAAL,GAAc,KAAK2D,WAAL,CAAiBtE,YAAY,CAAC4E,MAA9B,EAAsC,IAAtC,CAAd;AAEA,eAAKhE,YAAL,GAAoB,KAAK0D,WAAL,CAAiBtE,YAAY,CAAC6E,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKhE,eAAL,GAAuB,KAAKyD,WAAL,CAAiBtE,YAAY,CAAC8E,eAA9B,EAA+C,CAA/C,CAAvB;AACA,eAAK/D,YAAL,GAAoB,KAAKuD,WAAL,CAAiBtE,YAAY,CAAC+E,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAK/D,YAAL,GAAoB,KAAKsD,WAAL,CAAiBtE,YAAY,CAACgF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAK/D,SAAL,GAAiB,KAAKqD,WAAL,CAAiBtE,YAAY,CAACiF,SAA9B,EAAyC,CAAzC,CAAjB;AACA,eAAK/D,YAAL,GAAoB,KAAKoD,WAAL,CAAiBtE,YAAY,CAACkF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAK/D,YAAL,GAAoB,KAAKmD,WAAL,CAAiBtE,YAAY,CAACmF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAK/D,eAAL,GAAuB,KAAKkD,WAAL,CAAiBtE,YAAY,CAACoF,eAA9B,EAA+C,CAA/C,CAAvB;AACA,eAAK/D,cAAL,GAAsB,KAAKiD,WAAL,CAAiBtE,YAAY,CAACqF,cAA9B,EAA8C,CAA9C,CAAtB;AACA,eAAK/D,KAAL,GAAa,KAAKgD,WAAL,CAAiBtE,YAAY,CAACsF,KAA9B,EAAqC,CAArC,CAAb;AACA,eAAK/D,KAAL,GAAa,KAAK+C,WAAL,CAAiBtE,YAAY,CAACuF,KAA9B,EAAqC,CAArC,CAAb;AACA,eAAK/D,GAAL,GAAW,KAAK8C,WAAL,CAAiBtE,YAAY,CAACwF,GAA9B,EAAmC,CAAnC,CAAX;AACA,eAAK/D,MAAL,GAAc,KAAK6C,WAAL,CAAiBtE,YAAY,CAACyF,MAA9B,EAAsC,CAAtC,CAAd,CAtByB,CAwBzB;;AACA,eAAK9D,UAAL,GAAkB;AAAA;AAAA,iDAAlB,CAzByB,CA2BzB;;AACA,eAAKb,YAAL,CAAkB4E,EAAlB,CAAsBpC,KAAD,IAAW;AAC5B,gBAAIA,KAAK,GAAG,CAAR,IAAa;AAAA;AAAA,gCAAMqC,WAAN,EAAjB,EAAsC;AAClC,mBAAKpD,aAAL,GAAqB;AAAA;AAAA,kCAAMqD,WAAN,CAAkBC,WAAlB,CAA8BC,GAA9B,CAAkCxC,KAAlC,CAArB;;AACA,kBAAI,KAAKf,aAAT,EAAwB;AACpB;AAAA;AAAA,oCAAMwD,MAAN,CAAaC,IAAb,CAAkB,KAAKzD,aAAL,CAAmB0D,MAArC,EAA6CpH,MAA7C,EAAqD,CAACqH,KAAD,EAAaD,MAAb,KAAgC;AACjF,sBAAIC,KAAJ,EAAW;AACPC,oBAAAA,OAAO,CAACD,KAAR,CAAc,gCAAd,EAAgDA,KAAhD;AACA;AACH;;AACD,uBAAK/D,aAAL,GAAqB8D,MAArB;AACH,iBAND;AAOH;AACJ;AACJ,WAbD;AAcA,eAAKzF,QAAL,CAAckF,EAAd,CAAkBpC,KAAD,IAAW;AACxB,gBAAIA,KAAJ,EAAW;AACP;AAAA;AAAA,gDAAa8C,eAAb,CAA6B,IAA7B;AACH,aAFD,MAEO;AACH;AAAA;AAAA,gDAAaC,gBAAb,CAA8B,IAA9B;AACH;AACJ,WAND;AAOH;;AAESpD,QAAAA,iBAAiB,GAAG;AAC1B,cAAI,CAAC,KAAKI,WAAN,IAAqB,KAAKA,WAAL,CAAiBiD,cAAjB,CAAgCvC,MAAhC,IAA0C,CAAnE,EAAsE;AAEtE,eAAKnC,WAAL,GAAmB,EAAnB;AACA,cAAI2E,GAAG,GAAG;AAAA;AAAA,uDAAV;AACAA,UAAAA,GAAG,CAACC,OAAJ,GAAc,IAAd;AACAD,UAAAA,GAAG,CAACE,WAAJ,GAAkB;AAAA;AAAA,4CAAaA,WAA/B;;AACA,eAAK,MAAMC,UAAX,IAAyB,KAAKrD,WAAL,CAAiBiD,cAA1C,EAA0D;AACtD;AAAA;AAAA,8CAAaK,uBAAb,CAAqCJ,GAArC,EAA0CG,UAA1C;AACH;AACJ,SAjMiE,CAmMlE;;;AACUxD,QAAAA,eAAe,GAAG;AACxB,cAAI,CAAC,KAAKG,WAAV,EAAuB;AAEvB,eAAK7C,QAAL,CAAc8C,KAAd,GAAsB,KAAtB;AACA,eAAK5B,WAAL,CAAiB4B,KAAjB,GAAyB,CAAzB;AACA,eAAKxC,YAAL,CAAkBwC,KAAlB,GAA0B,KAAKC,QAA/B;AACA,eAAK9C,cAAL,CAAoB6C,KAApB,GAA4B,KAAKD,WAAL,CAAiB5C,cAA7C;AACA,eAAKC,SAAL,CAAe4C,KAAf,GAAuB,KAAKD,WAAL,CAAiB3C,SAAxC;AACA,eAAKC,MAAL,CAAY2C,KAAZ,GAAoB,KAAKD,WAAL,CAAiB1C,MAArC;AAEA,eAAKC,YAAL,CAAkB0C,KAAlB,GAA0B,KAAKD,WAAL,CAAiBzC,YAAjB,CAA8B4C,IAA9B,EAA1B;AACA,eAAK3C,eAAL,CAAqByC,KAArB,GAA6B,KAAKD,WAAL,CAAiBxC,eAAjB,CAAiC2C,IAAjC,EAA7B;AACA,eAAKzC,YAAL,CAAkBuC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBtC,YAAjB,CAA8ByC,IAA9B,EAA1B;AACA,eAAKxC,YAAL,CAAkBsC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBrC,YAAjB,CAA8BwC,IAA9B,EAA1B;AACA,eAAKvC,SAAL,CAAeqC,KAAf,GAAuB,KAAKD,WAAL,CAAiBpC,SAAjB,CAA2BuC,IAA3B,EAAvB;AACA,eAAKtC,YAAL,CAAkBoC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBnC,YAAjB,CAA8BsC,IAA9B,EAA1B;AACA,eAAKrC,YAAL,CAAkBmC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBlC,YAAjB,CAA8BqC,IAA9B,EAA1B;AACA,eAAKpC,eAAL,CAAqBkC,KAArB,GAA6B,KAAKD,WAAL,CAAiBjC,eAAjB,CAAiCoC,IAAjC,EAA7B;AACA,eAAKnC,cAAL,CAAoBiC,KAApB,GAA4B,KAAKD,WAAL,CAAiBhC,cAAjB,CAAgCmC,IAAhC,EAA5B;AACA,eAAKlC,KAAL,CAAWgC,KAAX,GAAmB,KAAKD,WAAL,CAAiB/B,KAAjB,CAAuBkC,IAAvB,EAAnB;AACA,eAAKjC,KAAL,CAAW+B,KAAX,GAAmB,KAAKD,WAAL,CAAiB9B,KAAjB,CAAuBiC,IAAvB,EAAnB;AACA,eAAKhC,GAAL,CAAS8B,KAAT,GAAiB,KAAKD,WAAL,CAAiB7B,GAAjB,CAAqBgC,IAArB,EAAjB;AACA,eAAK/B,MAAL,CAAY6B,KAAZ,GAAoB,KAAKD,WAAL,CAAiB5B,MAAjB,CAAwB+B,IAAxB,EAApB;AAEA,eAAK7B,UAAL,CAAgBiF,aAAhB,CAA8B,KAAKC,UAAnC;AAEA,eAAKpD,SAAL,CAAe,IAAf;AACH;;AAESqD,QAAAA,YAAY,CAACC,IAAD,EAAyBzD,KAAzB,EAAiD;AACnE;AACA;AACA,cAAIyD,IAAI,CAACC,QAAL,CAAc/G,SAAS,CAACgH,MAAxB,CAAJ,EAAqC;AACjCF,YAAAA,IAAI,CAACzD,KAAL,GAAaA,KAAK,CAACE,IAAN,EAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACIM,QAAAA,YAAY,CAACnB,MAAD,EAAyB;AACjC,cAAI,KAAKd,OAAL,KAAiBc,MAArB,EAA6B;AAE7B,gBAAMuE,SAAS,GAAG,KAAKrF,OAAvB;AACA,eAAKA,OAAL,GAAec,MAAf;AACA,eAAKZ,kBAAL,GAA0B,CAA1B;AACA,eAAKG,aAAL,GAAqB,CAArB,CANiC,CAOjC;;AACA,eAAKO,mBAAL,GAA2B,EAA3B;;AAEA,cAAIE,MAAM,KAAK5C,cAAc,CAACoH,OAA9B,EAAuC;AACnC,iBAAKzF,WAAL,CAAiB4B,KAAjB,GAAyB,CAAzB;AACH;;AACD,cAAIX,MAAM,KAAK5C,cAAc,CAAC+B,IAA9B,EAAoC;AAChC,gBAAI,KAAKF,WAAL,CAAiBmC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,mBAAKnC,WAAL,CAAiBoC,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACmD,OAAN,EAAlC;AACH;AACJ,WAJD,MAKK;AACD;AACA,gBAAI,KAAKxF,WAAL,CAAiBmC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,mBAAKnC,WAAL,CAAiBoC,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACoD,QAAN,EAAlC;AACH;AACJ;;AAED,cAAI,KAAK9G,8BAAL,IAAuC,IAA3C,EAAiD;AAC7C,iBAAKA,8BAAL,CAAoC,IAApC,EAA0C2G,SAA1C,EAAqDvE,MAArD;AACH;AACJ;;AAES2E,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAKR,YAAL,CAAkB,KAAK9F,YAAvB,EAAqC,KAAKqC,WAAL,CAAiBrC,YAAtD,EAFyB,CAIzB;;AACA,eAAKkB,aAAL,GAAqB,KAAKH,kBAAL,GAA0B,KAAKf,YAAL,CAAkBsC,KAAjE;AACH;;AAESiE,QAAAA,aAAa,GAAG;AACtB,eAAKtF,WAAL,GAAmB,IAAnB,CADsB,CAEtB;AACA;AACH;;AAESuF,QAAAA,YAAY,GAAG;AACrB,eAAKvF,WAAL,GAAmB,KAAnB,CADqB,CAErB;;AACA,eAAKQ,mBAAL,GAA2B,EAA3B;AACA,eAAKgF,sBAAL;AACH;;AAESC,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AACnB;AACA,eAAKb,YAAL,CAAkB,KAAKvF,KAAvB,EAA8B,KAAK8B,WAAL,CAAiB9B,KAA/C;AACA,eAAKuF,YAAL,CAAkB,KAAKtF,GAAvB,EAA4B,KAAK6B,WAAL,CAAiB7B,GAA7C;AACA,eAAKsF,YAAL,CAAkB,KAAKrF,MAAvB,EAA+B,KAAK4B,WAAL,CAAiB5B,MAAhD;AACA,eAAKqF,YAAL,CAAkB,KAAK3F,YAAvB,EAAqC,KAAKkC,WAAL,CAAiBlC,YAAtD;;AAEA,cAAI,KAAKC,eAAL,CAAqBkC,KAArB,GAA6B,CAAjC,EAAoC;AAChC;AACA,iBAAK,IAAIsE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKzG,YAAL,CAAkBmC,KAAtC,EAA6CsE,CAAC,EAA9C,EAAkD;AAC9C,mBAAKd,YAAL,CAAkB,KAAK1F,eAAvB,EAAwC,KAAKiC,WAAL,CAAiBjC,eAAzD;AACA,oBAAMyG,UAAU,GAAG,KAAK9F,kBAAL,GAA2B,KAAKX,eAAL,CAAqBkC,KAArB,GAA6BsE,CAA3E;;AACA,mBAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKvG,KAAL,CAAW+B,KAA/B,EAAsCwE,CAAC,EAAvC,EAA2C;AACvC,qBAAKrF,mBAAL,CAAyBsF,IAAzB,CAA8B;AAC1BC,kBAAAA,KAAK,EAAEF,CADmB;AAE1BG,kBAAAA,YAAY,EAAEL,CAFY;AAG1BC,kBAAAA,UAAU,EAAEA;AAHc,iBAA9B;AAKH;AACJ;AACJ,WAbD,MAcK;AACD;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKvG,KAAL,CAAW+B,KAA/B,EAAsCwE,CAAC,EAAvC,EAA2C;AACvC,mBAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKzG,YAAL,CAAkBmC,KAAtC,EAA6CsE,CAAC,EAA9C,EAAkD;AAC9C,qBAAKM,UAAL,CAAgBJ,CAAhB,EAAmBF,CAAnB;AACH;AACJ;AACJ;AACJ;;AAESO,QAAAA,mBAAmB,GAAS;AAClC;AACA,iBAAO,KAAK1F,mBAAL,CAAyBsB,MAAzB,GAAkC,CAAzC,EAA4C;AACxC,kBAAMqE,UAAU,GAAG,KAAK3F,mBAAL,CAAyB,CAAzB,CAAnB,CADwC,CAGxC;;AACA,gBAAI,KAAKV,kBAAL,IAA2BqG,UAAU,CAACP,UAA1C,EAAsD;AAClD;AACA,mBAAKpF,mBAAL,CAAyB4F,KAAzB;;AACA,mBAAKH,UAAL,CAAgBE,UAAU,CAACJ,KAA3B,EAAkCI,UAAU,CAACH,YAA7C;AACH,aAJD,MAIO;AACH;AACA;AACH;AACJ;AACJ;;AAESK,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKZ,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESO,QAAAA,UAAU,CAACF,KAAD,EAAgBC,YAAhB,EAAsC;AACtD,gBAAMM,SAAS,GAAG,KAAKC,iBAAL,CAAuBR,KAAvB,CAAlB;AACA,gBAAMS,QAAQ,GAAG,KAAKC,gBAAL,CAAsBV,KAAtB,EAA6BC,YAA7B,CAAjB;AACA,eAAKU,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACR,KAAD,EAA0C;AACvD;AACA,eAAKlB,YAAL,CAAkB,KAAKxF,KAAvB,EAA8B,KAAK+B,WAAL,CAAiB/B,KAA/C,EAFuD,CAGvD;;AACA,gBAAMsH,WAAW,GAAG,KAAKrH,KAAL,CAAW+B,KAAX,GAAmB,CAAnB,GAAwB,KAAK9B,GAAL,CAAS8B,KAAT,IAAkB,KAAK/B,KAAL,CAAW+B,KAAX,GAAmB,CAArC,CAAD,GAA4C0E,KAA5C,GAAoD,KAAKxG,GAAL,CAAS8B,KAAT,GAAiB,CAA5F,GAAgG,CAApH;AACA,gBAAMuF,MAAM,GAAGhJ,gBAAgB,CAAC,KAAKyB,KAAL,CAAWgC,KAAX,GAAmBsF,WAApB,CAA/B;AAEA,iBAAO;AACHE,YAAAA,CAAC,EAAEC,IAAI,CAACC,GAAL,CAASH,MAAT,CADA;AAEHI,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIH,QAAAA,gBAAgB,CAACV,KAAD,EAAgBC,YAAhB,EAAgE;AAC5E;AACA;AACA,gBAAMkB,cAAc,GAAG,CAAClB,YAAD,EAAuB9G,YAAvB,EAA6CE,cAA7C,KAAwE;AAC3F,gBAAIF,YAAY,IAAI,CAAhB,IAAqBE,cAAc,KAAK,CAA5C,EAA+C,OAAO,CAAP;AAC/C,kBAAM+H,QAAQ,GAAG/H,cAAc,IAAIF,YAAY,GAAG,CAAnB,CAA/B,CAF2F,CAG3F;;AAEA,gBAAIA,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,kBAAI8G,YAAY,KAAK,CAArB,EAAwB,OAAO,CAAP;;AACxB,kBAAIA,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,sBAAMoB,eAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;AACA,uBAAO,CAACoB,eAAD,GAAmBD,QAA1B;AACH,eAJD,MAKK;AACD;AACA,sBAAMC,eAAe,GAAGN,IAAI,CAACQ,IAAL,CAAUtB,YAAY,GAAG,CAAzB,CAAxB;AACA,uBAAOoB,eAAe,GAAGD,QAAzB;AACH;AACJ,aAbD,MAaO;AACH;AACA,kBAAInB,YAAY,KAAK,CAArB,EAAwB,OAAO,CAACmB,QAAD,GAAY,CAAnB;;AACxB,kBAAInB,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,sBAAMoB,eAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;AACA,uBAAO,CAACmB,QAAD,GAAY,CAAZ,GAAgBC,eAAe,GAAGD,QAAzC;AACH,eAJD,MAKK;AACD;AACA,sBAAMC,eAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;AACA,uBAAOmB,QAAQ,GAAG,CAAX,GAAeC,eAAe,GAAGD,QAAxC;AACH;AACJ;AACJ,WAhCD;;AAkCA,eAAKtC,YAAL,CAAkB,KAAKzF,cAAvB,EAAuC,KAAKgC,WAAL,CAAiBhC,cAAxD;AACA,gBAAMA,cAAc,GAAG8H,cAAc,CAAClB,YAAD,EAAe,KAAK9G,YAAL,CAAkBmC,KAAjC,EAAwC,KAAKjC,cAAL,CAAoBiC,KAA5D,CAArC;;AACA,cAAI,KAAK7B,MAAL,CAAY6B,KAAZ,IAAqB,CAAzB,EAA4B;AACxB,mBAAO;AAAEwF,cAAAA,CAAC,EAAEzH,cAAL;AAAqB4H,cAAAA,CAAC,EAAE;AAAxB,aAAP;AACH;;AAED,gBAAMV,SAAS,GAAG,KAAKC,iBAAL,CAAuBR,KAAvB,CAAlB,CA3C4E,CA4C5E;;AACA,gBAAMwB,aAAa,GAAG;AAAEV,YAAAA,CAAC,EAAE,CAACP,SAAS,CAACU,CAAhB;AAAmBA,YAAAA,CAAC,EAAEV,SAAS,CAACO;AAAhC,WAAtB;;AACA,cAAI,KAAKrH,MAAL,CAAY6B,KAAZ,IAAqB,CAAzB,EAA4B;AACxB,mBAAO;AACHwF,cAAAA,CAAC,EAAEU,aAAa,CAACV,CAAd,GAAkBzH,cADlB;AAEH4H,cAAAA,CAAC,EAAEO,aAAa,CAACP,CAAd,GAAkB5H;AAFlB,aAAP;AAIH;;AAED,iBAAO;AACHyH,YAAAA,CAAC,EAAEP,SAAS,CAACO,CAAV,GAAc,KAAKrH,MAAL,CAAY6B,KAA1B,GAAkCkG,aAAa,CAACV,CAAd,GAAkBzH,cADpD;AAEH4H,YAAAA,CAAC,EAAEV,SAAS,CAACU,CAAV,GAAc,KAAKxH,MAAL,CAAY6B,KAA1B,GAAkCkG,aAAa,CAACP,CAAd,GAAkB5H;AAFpD,WAAP;AAIH;;AAEDsH,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAAgF;AACxF,cAAI,CAAC,KAAKtG,aAAV,EAAyB;AACrB,gBAAI,KAAKsH,YAAT,EAAuB;AACnB,mBAAKtH,aAAL,GAAqB,KAAKsH,YAA1B;AACH,aAFD,MAGK;AACD,kBAAI3K,MAAJ,EAAY;AACR,qBAAK4K,oBAAL,CAA0BnB,SAA1B,EAAqCE,QAArC;AACH;;AACD;AACH;AACJ;;AAED,gBAAMkB,MAAM,GAAG,KAAKC,iBAAL,EAAf;AACA,cAAI,CAACD,MAAL,EAAa;AAEb;AAAA;AAAA,4CAAaE,cAAb,CAA4B,IAA5B,EAAkCF,MAAlC,EAhBwF,CAiBxF;;AACA,gBAAMG,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAL,UAAAA,MAAM,CAACI,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAAChB,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIgB,UAAU,CAACb,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIa,UAAU,CAACI,CAHf;AAKAP,UAAAA,MAAM,CAAC5C,IAAP,CAAYoD,UAAZ,CAAuB7G,KAAvB,GAA+BxD,gBAAgB,CAACiJ,IAAI,CAACqB,KAAL,CAAW7B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA/C;AACAa,UAAAA,MAAM,CAAC5C,IAAP,CAAYsD,KAAZ,CAAkB/G,KAAlB,IAA2B,KAAKrC,SAAL,CAAeqC,KAA1C,CAzBwF,CA0BxF;AACA;;AACAqG,UAAAA,MAAM,CAACW,OAAP;;AAEA,cAAI,KAAKhK,uBAAL,IAAgC,IAApC,EAA0C;AACtC,iBAAKA,uBAAL,CAA6BqJ,MAA7B;AACH;AACJ;;AAEmC,cAApBD,oBAAoB,CAACnB,SAAD,EAAsCE,QAAtC,EAA0E;AAC1G;AACA,gBAAM8B,UAAU,GAAG,sDAAnB,CAF0G,CAG1G;;AACAC,UAAAA,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,YAAnC,EAAiDH,UAAjD,EACKI,IADL,CACWC,IAAD,IAAkB;AACpBjM,YAAAA,YAAY,CAACkM,OAAb,CAAqB;AAAED,cAAAA,IAAI,EAAEA;AAAR,aAArB,EAAqC,CAACE,GAAD,EAAM7E,MAAN,KAAiB;AAClD,kBAAI6E,GAAJ,EAAS;AACL3E,gBAAAA,OAAO,CAACD,KAAR,CAAc4E,GAAd;AACA;AACH;;AACD,mBAAK3I,aAAL,GAAqB8D,MAArB;AACA,oBAAM0D,MAAM,GAAG,KAAKC,iBAAL,EAAf;AACA,kBAAI,CAACD,MAAL,EAAa;AAEb;AAAA;AAAA,gDAAaE,cAAb,CAA4B,IAA5B,EAAkCF,MAAlC,EATkD,CAUlD;;AACA,oBAAMG,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAL,cAAAA,MAAM,CAACI,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAAChB,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIgB,UAAU,CAACb,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIa,UAAU,CAACI,CAHf;AAKAP,cAAAA,MAAM,CAAC5C,IAAP,CAAYoD,UAAZ,CAAuB7G,KAAvB,GAA+BxD,gBAAgB,CAACiJ,IAAI,CAACqB,KAAL,CAAW7B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA/C;AACAa,cAAAA,MAAM,CAAC5C,IAAP,CAAYsD,KAAZ,CAAkB/G,KAAlB,IAA2B,KAAKrC,SAAL,CAAeqC,KAA1C;AACAqG,cAAAA,MAAM,CAACW,OAAP;AACH,aApBD;AAqBH,WAvBL;AAwBH;;AAESV,QAAAA,iBAAiB,GAAkB;AACzC,gBAAMmB,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAaC,YAAhC,EAA8C,KAAK9I,aAAnD,CAAnB;;AACA,cAAI,CAAC4I,UAAL,EAAiB;AACb5E,YAAAA,OAAO,CAACD,KAAR,CAAc,8CAAd;AACA,mBAAO,IAAP;AACH,WALwC,CAOzC;;;AACA,gBAAMyD,MAAM,GAAGoB,UAAU,CAACG,YAAX;AAAA;AAAA,+BAAf;;AACA,cAAI,CAACvB,MAAL,EAAa;AACTxD,YAAAA,OAAO,CAACD,KAAR,CAAc,uDAAd;AACA6E,YAAAA,UAAU,CAACI,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAIrM,MAAJ,EAAY;AACRiM,YAAAA,UAAU,CAACK,IAAX,GAAkBlL,OAAO,CAACmL,mBAA1B;AACH;;AAED,iBAAO1B,MAAP;AACH;;AAED2B,QAAAA,UAAU,CAACrF,MAAD,EAAiBwC,QAAjB,EAAiC8C,QAAjC,EAAiDC,QAAjD,EAAmE;AACzE,cAAI,CAACvF,MAAL,EAAa;AAEb,gBAAMwF,UAAU,GAAG;AAAA;AAAA,wCAAWT,OAAX,CAAmB,KAAKjB,IAAxB,EAA8B9D,MAA9B,CAAnB;AACA,cAAI,CAACwF,UAAL,EAAiB;AAEjBA,UAAAA,UAAU,CAACxB,gBAAX,CAA4BxB,QAA5B;AACAgD,UAAAA,UAAU,CAACC,gBAAX,CAA4BH,QAA5B,EAPyE,CAQzE;AACA;;AACA,eAAKI,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,0CAAWC,UAAX,CAAsBH,UAAtB;AACH,WAFD,EAEGD,QAFH;AAGH;AAED;AACJ;AACA;;;AACcK,QAAAA,UAAU,GAAY;AAC5B;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,IAAI,CAACC,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAKvL,QAAN,IAAkB,CAAC,KAAKA,QAAL,CAAc8C,KAArC,EAA4C;AACxC;AACH;;AAED,kBAAQ,KAAKzB,OAAb;AACI,iBAAK9B,cAAc,CAAC+B,IAApB;AACI,mBAAKkK,gBAAL;AACA;;AACJ,iBAAKjM,cAAc,CAACoH,OAApB;AACI,mBAAK8E,mBAAL;AACA;;AACJ,iBAAKlM,cAAc,CAACmM,QAApB;AACI,mBAAKC,oBAAL;AACA;;AACJ,iBAAKpM,cAAc,CAACqM,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAKtM,cAAc,CAACuM,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAjBR;;AAoBA,eAAK7K,WAAL,CAAiB4B,KAAjB,IAA0ByI,SAA1B;AACA,eAAKhK,kBAAL,IAA2BgK,SAA3B;AACA,eAAK/J,iBAAL,IAA0B+J,SAA1B;AAEA,eAAKtI,SAAL;AACH;;AAESuI,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAKjK,kBAAL,IAA2B,KAAKnB,YAAL,CAAkB0C,KAAjD,EAAwD;AACpD,iBAAKQ,YAAL,CAAkB/D,cAAc,CAACoH,OAAjC;AACH;AACJ;;AAES8E,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAKvL,SAAL,CAAe4C,KAApB,EACI,KAAKQ,YAAL,CAAkB/D,cAAc,CAACmM,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAKnK,kBAAL,IAA2B,KAAKlB,eAAL,CAAqByC,KAApD,EAA2D;AACvD,mBAAKQ,YAAL,CAAkB/D,cAAc,CAACmM,QAAjC;AACH;AACJ;AACJ;;AAESC,QAAAA,oBAAoB,GAAG;AAC7B,cAAI,KAAKpK,kBAAL,GAA0B,KAAKhB,YAAL,CAAkBuC,KAAhD,EAAuD;AACnD,iBAAKkE,YAAL;AACA,gBAAI,KAAK7G,MAAT,EACI,KAAKmD,YAAL,CAAkB/D,cAAc,CAACqM,cAAjC,EADJ,KAGI,KAAKtI,YAAL,CAAkB/D,cAAc,CAACuM,SAAjC;AACJ;AACH,WAR4B,CAU7B;;;AACA,cAAI,CAAC,KAAKrK,WAAV,EAAuB;AACnB,iBAAKsF,aAAL;AACH,WAFD,MAGK,IAAI,KAAKxF,kBAAL,IAA2B,KAAKG,aAApC,EAAmD;AACpD,iBAAKoG,OAAL;;AACA,gBAAI,KAAKlH,eAAL,CAAqBkC,KAArB,IAA8B,CAAlC,EAAqC;AACjC,mBAAKgE,gBAAL;AACH,aAFD,MAGK;AACD;AACA,mBAAKpF,aAAL,GAAqB,KAAKH,kBAAL,GAA0B,QAA/C;AACH;AACJ;;AAED,cAAIyK,WAAW,GAAG,KAAK/J,mBAAL,CAAyBsB,MAAzB,GAAkC,CAApD,CAzB6B,CA0B7B;;AACA,eAAKoE,mBAAL;;AACA,cAAIqE,WAAW,IAAI,KAAK/J,mBAAL,CAAyBsB,MAAzB,IAAmC,CAAtD,EAAyD;AACrD,iBAAKuD,gBAAL;AACH;AACJ;;AAES+E,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAKtK,kBAAL,IAA2B,KAAKb,YAAL,CAAkBoC,KAAjD,EAAwD;AACpD,iBAAKQ,YAAL,CAAkB/D,cAAc,CAACoH,OAAjC;AACH;AACJ;;AAESoF,QAAAA,qBAAqB,GAAG;AAC9B;AACA,eAAK/L,QAAL,CAAc8C,KAAd,GAAsB,KAAtB;AACA,eAAK9C,QAAL,CAAcoD,MAAd;AACH;;AA/nBiE,O,UAE3DyH,mB,GAA8B,U;;;;;iBAGT,C;;;;;;;;;;;;iBAMQ;AAAA;AAAA,2C;;;;;;;iBAGF;AAAA;AAAA,yC", "sourcesContent": ["import { _decorator, assetManager, misc, Prefab, Quat, Vec3 } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { ResBullet } from 'db://assets/bundles/common/script/autogen/luban/schema';\r\nimport Entity from 'db://assets/bundles/common/script/game/ui/base/Entity';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { EmitterData } from '../data/bullet/EmitterData';\r\nimport { Bullet, BulletProperty } from './Bullet';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { EventGroup, EventGroupContext } from \"./EventGroup\";\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { Property, PropertyContainerComponent } from './PropertyContainer';\r\nimport PlaneBase from '../ui/plane/PlaneBase';\r\nimport { ExpressionValue } from '../data/bullet/ExpressionValue';\r\n\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu } = _decorator;\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\n\r\n/**\r\n * 发射器状态变换\r\n * [None] -> <InitialDelay> -> [Prewarm] -> <PrewarmDuration> -> [Emitting]\r\n *                                 ^                                 |\r\n *                                 |                                 v\r\n *                                 |                           <EmitDuration>\r\n *                                 |                                 |\r\n *                                 |                              isLoop? ---no---> [Completed] \r\n *                                 |                                 |\r\n *                                 |                                 |yes\r\n *                                 |                                 |\r\n *                                 |                                 v\r\n *                                 -------<LoopInterval>------[LoopEndReached]\r\n */\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n// 用枚举定义属性\r\nexport enum eEmitterProp {\r\n    IsActive = 1, IsOnlyInScreen, IsPreWarm, IsLoop,\r\n    InitialDelay, PrewarmDuration, EmitBulletID, EmitDuration, EmitInterval, EmitPower, LoopInterval,\r\n    PerEmitCount, PerEmitInterval, PerEmitOffsetX,\r\n    Angle, Count, Arc, Radius,\r\n    ElapsedTime,\r\n}\r\n\r\n/**\r\n * 说明：\r\n * 因为发射器属性可能需要从emitterData里的公式计算，如: randi(0,360);\r\n * 但同时也可能被事件组修改，参考: EmitterEventActions\r\n * 事件组的优先级要高于emitterData的公式计算, 因此, 如果一个属性带了ePropMask.EventGroup标记, 后续ReEval时就直接跳过\r\n */\r\nexport enum ePropMask {\r\n    ReEval = 1 << 0, // 需要重新从公式计算\r\n    EventGroup = 1 << 1, // 需要被事件组修改\r\n}\r\n\r\nexport type onBulletCreatedDelegate = (bullet: Bullet) => void;\r\nexport type onEmitterStatusChangedDelegate = (emitter: Emitter, oldStatus: eEmitterStatus, newStatus: eEmitterStatus) => void;\r\n\r\n/**\r\n * 目前Emitter,EventGroup,BulletSystem的状态管理还是比较混乱\r\n * 需要看下怎么调整，使代码不论是运行时，还是编辑器下，都更加健壮\r\n * - young\r\n */\r\n@ccclass('Emitter')\r\n// @inspector('editor/inspector/components/emitter')\r\n@menu('子弹系统/发射器')\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class Emitter extends PropertyContainerComponent<eEmitterProp> {\r\n\r\n    static kBulletNameInEditor: string = \"_bullet_\";\r\n\r\n    @property({ displayName: \"子弹ID\" })\r\n    readonly bulletID: number = 0;\r\n\r\n    @property({ type: Prefab, displayName: \"子弹Prefab(仅编辑器下)\", editorOnly: true })\r\n    readonly bulletPrefab!: Prefab;\r\n\r\n    @property({ type: EmitterData, displayName: \"发射器属性\" })\r\n    readonly emitterData: EmitterData = new EmitterData();\r\n\r\n    @property({ type: BulletData, displayName: \"子弹属性\" })\r\n    readonly bulletData: BulletData = new BulletData();\r\n\r\n    // callbacks\r\n    onBulletCreatedCallback: onBulletCreatedDelegate | null = null;\r\n    onEmitterStatusChangedCallback: onEmitterStatusChangedDelegate | null = null;\r\n\r\n    // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)\r\n    public isActive!: Property<boolean>;\r\n    public isOnlyInScreen!: Property<boolean>;\r\n    public isPreWarm!: Property<boolean>;\r\n    public isLoop!: Property<boolean>;\r\n    public initialDelay!: Property<number>;\r\n    public preWarmDuration!: Property<number>;\r\n    public emitBulletID!: Property<number>;\r\n    public emitDuration!: Property<number>;\r\n    public emitInterval!: Property<number>;\r\n    public emitPower!: Property<number>;\r\n    public loopInterval!: Property<number>;\r\n    public perEmitCount!: Property<number>;\r\n    public perEmitInterval!: Property<number>;\r\n    public perEmitOffsetX!: Property<number>;\r\n    public angle!: Property<number>;\r\n    public count!: Property<number>;\r\n    public arc!: Property<number>;\r\n    public radius!: Property<number>;\r\n    public elapsedTime!: Property<number>; \r\n    // 以下用于事件组修改子弹的属性，（不直接修改bulletData)\r\n    public bulletProp!: BulletProperty;\r\n\r\n    // 发射器自己的事件组\r\n    public eventGroups: EventGroup[] = [];\r\n\r\n    // 私有变量\r\n    protected _status: eEmitterStatus = eEmitterStatus.None;\r\n    protected _statusElapsedTime: number = 0;\r\n    protected _totalElapsedTime: number = 0;\r\n    protected _isEmitting: boolean = false;\r\n    protected _nextEmitTime: number = 0;\r\n    protected _bulletPrefab: Prefab | null = null;\r\n    protected _prewarmEffectPrefab: Prefab | null = null;\r\n    protected _emitEffectPrefab: Prefab | null = null;\r\n    protected _entity: PlaneBase | null = null;\r\n    protected _bulletConfig: ResBullet | undefined = undefined;\r\n    \r\n    // Per-emit timing tracking\r\n    protected _perEmitBulletQueue: Array<{ index: number, perEmitIndex: number, targetTime: number }> = [];\r\n\r\n    get isEmitting(): boolean { return this._isEmitting; }\r\n    get status(): eEmitterStatus { return this._status; }\r\n    get statusElapsedTime(): number { return this._statusElapsedTime; }\r\n    get totalElapsedTime(): number { return this._totalElapsedTime; }\r\n    get bulletConfig(): ResBullet | undefined { return this._bulletConfig; }\r\n\r\n    protected onLoad(): void {\r\n        this.createProperties();\r\n        this.createEventGroups();\r\n\r\n        // 更新属性\r\n        this.resetProperties();\r\n    }\r\n\r\n    //#region \"Editor Region\"\r\n    public onLostFocusInEditor(): void {\r\n        this.updatePropertiesInEditor();\r\n        this.createEventGroups();\r\n    }\r\n\r\n    public updatePropertiesInEditor() {\r\n        if (!this.emitterData) return;\r\n\r\n        this.isActive.value = true;\r\n        this.emitBulletID.value = this.bulletID;\r\n        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm.value = this.emitterData.isPreWarm;\r\n        this.isLoop.value = this.emitterData.isLoop;\r\n\r\n        this.initialDelay.value = this.emitterData.initialDelay.eval();\r\n        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval();\r\n        this.emitDuration.value = this.emitterData.emitDuration.eval();\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval();\r\n        this.emitPower.value = this.emitterData.emitPower.eval();\r\n        this.loopInterval.value = this.emitterData.loopInterval.eval();\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval();\r\n        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();\r\n        this.angle.value = this.emitterData.angle.eval();\r\n        this.count.value = this.emitterData.count.eval();\r\n        this.arc.value = this.emitterData.arc.eval();\r\n        this.radius.value = this.emitterData.radius.eval();\r\n\r\n        this.notifyAll(true);\r\n    }\r\n    //#endregion \"Editor Region\"\r\n\r\n    // 通过这个接口来启用和禁用发射器\r\n    public setIsActive(active: boolean) {\r\n        this.isActive.value = active;\r\n        this.isActive.notify();\r\n    }\r\n\r\n    // 这个接口清理发射器的状态，全部从头开始\r\n    public reset() {\r\n        this._isEmitting = false;\r\n        this.changeStatus(eEmitterStatus.None);\r\n        this.resetProperties();\r\n        if (this.eventGroups.length > 0) {\r\n            this.eventGroups.forEach(group => group.reset());\r\n        }\r\n    }\r\n\r\n    public setEntity(entity: PlaneBase) {\r\n        this._entity = entity;\r\n    }\r\n\r\n    public getEntity(): PlaneBase | null {\r\n        return this._entity;\r\n    }\r\n\r\n    protected createProperties() {\r\n        this.clear();\r\n\r\n        this.isActive = this.addProperty(eEmitterProp.IsActive, false);\r\n        this.elapsedTime = this.addProperty(eEmitterProp.ElapsedTime, 0);\r\n        this.emitBulletID = this.addProperty(eEmitterProp.EmitBulletID, this.bulletID);\r\n        this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, true);\r\n        this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, true);\r\n        this.isLoop = this.addProperty(eEmitterProp.IsLoop, true);\r\n\r\n        this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, 0);\r\n        this.preWarmDuration = this.addProperty(eEmitterProp.PrewarmDuration, 0);\r\n        this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, 0);\r\n        this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, 0);\r\n        this.emitPower = this.addProperty(eEmitterProp.EmitPower, 1);\r\n        this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, 0);\r\n        this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, 1);\r\n        this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, 0);\r\n        this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, 0);\r\n        this.angle = this.addProperty(eEmitterProp.Angle, 0);\r\n        this.count = this.addProperty(eEmitterProp.Count, 1);\r\n        this.arc = this.addProperty(eEmitterProp.Arc, 0);\r\n        this.radius = this.addProperty(eEmitterProp.Radius, 0);\r\n\r\n        // 子弹相关属性\r\n        this.bulletProp = new BulletProperty();\r\n\r\n        // 子弹表->Prefab路径\r\n        this.emitBulletID.on((value) => {\r\n            if (value > 0 && MyApp.GetInstance()) {\r\n                this._bulletConfig = MyApp.lubanTables.TbResBullet.get(value);\r\n                if (this._bulletConfig) {\r\n                    MyApp.resMgr.load(this._bulletConfig.prefab, Prefab, (error: any, prefab: Prefab) => {\r\n                        if (error) {\r\n                            console.error(\"Emitter load bullet prefab err\", error);\r\n                            return;\r\n                        }\r\n                        this._bulletPrefab = prefab;\r\n                    });\r\n                }\r\n            }\r\n        });\r\n        this.isActive.on((value) => {\r\n            if (value) {\r\n                BulletSystem.onCreateEmitter(this);\r\n            } else {\r\n                BulletSystem.onDestroyEmitter(this);\r\n            }\r\n        });\r\n    }\r\n\r\n    protected createEventGroups() {\r\n        if (!this.emitterData || this.emitterData.eventGroupData.length <= 0) return;\r\n\r\n        this.eventGroups = [];\r\n        let ctx = new EventGroupContext();\r\n        ctx.emitter = this;\r\n        ctx.playerPlane = BulletSystem.playerPlane;\r\n        for (const eventGroup of this.emitterData.eventGroupData) {\r\n            BulletSystem.createEmitterEventGroup(ctx, eventGroup);\r\n        }\r\n    }\r\n\r\n    // reset properties from emitterData\r\n    protected resetProperties() {\r\n        if (!this.emitterData) return;\r\n\r\n        this.isActive.value = false;\r\n        this.elapsedTime.value = 0;\r\n        this.emitBulletID.value = this.bulletID;\r\n        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm.value = this.emitterData.isPreWarm;\r\n        this.isLoop.value = this.emitterData.isLoop;\r\n\r\n        this.initialDelay.value = this.emitterData.initialDelay.eval();\r\n        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval();\r\n        this.emitDuration.value = this.emitterData.emitDuration.eval();\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval();\r\n        this.emitPower.value = this.emitterData.emitPower.eval();\r\n        this.loopInterval.value = this.emitterData.loopInterval.eval();\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval();\r\n        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();\r\n        this.angle.value = this.emitterData.angle.eval();\r\n        this.count.value = this.emitterData.count.eval();\r\n        this.arc.value = this.emitterData.arc.eval();\r\n        this.radius.value = this.emitterData.radius.eval();\r\n\r\n        this.bulletProp.resetFromData(this.bulletData);\r\n\r\n        this.notifyAll(true);\r\n    }\r\n\r\n    protected evalProperty(prop: Property<number>, value: ExpressionValue) {\r\n        // 为什么这样写，而不是直接：prop.setValue(value.eval(), ePropMask.ReEval);\r\n        // 是为了避免非必要的eval()调用\r\n        if (prop.canWrite(ePropMask.ReEval)) {\r\n            prop.value = value.eval();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        if (this._status === status) return;\r\n\r\n        const oldStatus = this._status;\r\n        this._status = status;\r\n        this._statusElapsedTime = 0;\r\n        this._nextEmitTime = 0;\r\n        // Clear per-emit queue when changing status\r\n        this._perEmitBulletQueue = [];\r\n\r\n        if (status === eEmitterStatus.Prewarm) {\r\n            this.elapsedTime.value = 0;\r\n        }\r\n        if (status === eEmitterStatus.None) {\r\n            if (this.eventGroups.length > 0) {\r\n                this.eventGroups.forEach(group => group.tryStop());\r\n            }\r\n        }\r\n        else {\r\n            // 所有其他状态，都尝试开始执行eventGroup\r\n            if (this.eventGroups.length > 0) {\r\n                this.eventGroups.forEach(group => group.tryStart());\r\n            }\r\n        }\r\n\r\n        if (this.onEmitterStatusChangedCallback != null) {\r\n            this.onEmitterStatusChangedCallback(this, oldStatus, status);\r\n        }\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // re-eval\r\n        this.evalProperty(this.emitInterval, this.emitterData.emitInterval);\r\n\r\n        // Schedule the next emit after emitInterval\r\n        this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this._isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n    }\r\n\r\n    protected stopEmitting() {\r\n        this._isEmitting = false;\r\n        // Clear the per-emit bullet queue\r\n        this._perEmitBulletQueue = [];\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        // re-eval\r\n        this.evalProperty(this.count, this.emitterData.count);\r\n        this.evalProperty(this.arc, this.emitterData.arc);\r\n        this.evalProperty(this.radius, this.emitterData.radius);\r\n        this.evalProperty(this.perEmitCount, this.emitterData.perEmitCount);\r\n        \r\n        if (this.perEmitInterval.value > 0) {\r\n            // Generate bullets in time-sorted order directly\r\n            for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                this.evalProperty(this.perEmitInterval, this.emitterData.perEmitInterval);\r\n                const targetTime = this._statusElapsedTime + (this.perEmitInterval.value * j);\r\n                for (let i = 0; i < this.count.value; i++) {\r\n                    this._perEmitBulletQueue.push({\r\n                        index: i,\r\n                        perEmitIndex: j,\r\n                        targetTime: targetTime\r\n                    });\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // Immediate emission - no timing needed\r\n            for (let i = 0; i < this.count.value; i++) {\r\n                for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                    this.emitSingle(i, j);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected processPerEmitQueue(): void {\r\n        // Process bullets that should be emitted based on current time\r\n        while (this._perEmitBulletQueue.length > 0) {\r\n            const nextBullet = this._perEmitBulletQueue[0];\r\n\r\n            // Check if it's time to emit this bullet\r\n            if (this._statusElapsedTime >= nextBullet.targetTime) {\r\n                // Remove from queue and emit\r\n                this._perEmitBulletQueue.shift();\r\n                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);\r\n            } else {\r\n                // No more bullets ready to emit yet\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index: number, perEmitIndex: number) {\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index, perEmitIndex);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 期望如果配了公式，每次发射方向都随机下\r\n        this.evalProperty(this.angle, this.emitterData.angle);\r\n        // 计算发射方向\r\n        const angleOffset = this.count.value > 1 ? (this.arc.value / (this.count.value - 1)) * index - this.arc.value / 2 : 0;\r\n        const radian = degreesToRadians(this.angle.value + angleOffset);\r\n\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n    \r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * odd number to the right, even number to the left\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {\r\n        // add perEmitOffsetX by perEmitIndex, with the rules:\r\n        // by the following order:0, 1; 2, 0, 1; 2, 0, 1, 3;\r\n        const getEmitOffsetX = (perEmitIndex: number, perEmitCount: number, perEmitOffsetX: number) => {\r\n            if (perEmitCount <= 1 || perEmitOffsetX === 0) return 0;\r\n            const interval = perEmitOffsetX / (perEmitCount - 1);\r\n            //const middle = 0;\r\n\r\n            if (perEmitCount % 2 === 1) {\r\n                // 奇数情况\r\n                if (perEmitIndex === 0) return 0;\r\n                if (perEmitIndex % 2 === 0) {\r\n                    // 偶数索引在左边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return -stepsFromMiddle * interval;\r\n                }\r\n                else {\r\n                    // 奇数索引在右边\r\n                    const stepsFromMiddle = Math.ceil(perEmitIndex / 2);\r\n                    return stepsFromMiddle * interval;\r\n                }\r\n            } else {\r\n                // 偶数情况\r\n                if (perEmitIndex === 0) return -interval / 2;\r\n                if (perEmitIndex % 2 === 0) {\r\n                    // 偶数索引在左边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return -interval / 2 - stepsFromMiddle * interval;\r\n                }\r\n                else {\r\n                    // 奇数索引在右边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return interval / 2 + stepsFromMiddle * interval;\r\n                }\r\n            }\r\n        }\r\n\r\n        this.evalProperty(this.perEmitOffsetX, this.emitterData.perEmitOffsetX);\r\n        const perEmitOffsetX = getEmitOffsetX(perEmitIndex, this.perEmitCount.value, this.perEmitOffsetX.value);\r\n        if (this.radius.value <= 0) {\r\n            return { x: perEmitOffsetX, y: 0 };\r\n        }\r\n\r\n        const direction = this.getSpawnDirection(index);\r\n        // 计算垂直于发射方向的向量（逆时针90度旋转）\r\n        const perpendicular = { x: -direction.y, y: direction.x };\r\n        if (this.radius.value <= 0) {\r\n            return {\r\n                x: perpendicular.x * perEmitOffsetX,\r\n                y: perpendicular.y * perEmitOffsetX\r\n            };\r\n        }\r\n\r\n        return {\r\n            x: direction.x * this.radius.value + perpendicular.x * perEmitOffsetX,\r\n            y: direction.y * this.radius.value + perpendicular.y * perEmitOffsetX\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }): void {\r\n        if (!this._bulletPrefab) {\r\n            if (this.bulletPrefab) {\r\n                this._bulletPrefab = this.bulletPrefab;\r\n            }\r\n            else {\r\n                if (EDITOR) {\r\n                    this.createBulletInEditor(direction, position);\r\n                }\r\n                return;\r\n            }\r\n        }\r\n\r\n        const bullet = this.instantiateBullet();\r\n        if (!bullet) return;\r\n\r\n        BulletSystem.onCreateBullet(this, bullet);\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bullet.node.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n        bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n        bullet.prop.speed.value *= this.emitPower.value;\r\n        // 为什么需要在这里resetEventGroups?\r\n        // 因为EventGroups的条件初始化依赖上面先初始化子弹的属性\r\n        bullet.onReady();\r\n\r\n        if (this.onBulletCreatedCallback != null) {\r\n            this.onBulletCreatedCallback(bullet);\r\n        }\r\n    }\r\n\r\n    protected async createBulletInEditor(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        // use a default bullet prefab\r\n        const prefabPath = 'db://assets/resources/game/prefabs/Bullet_New.prefab';\r\n        // @ts-ignore\r\n        Editor.Message.request('asset-db', 'query-uuid', prefabPath)\r\n            .then((uuid: string) => {\r\n                assetManager.loadAny({ uuid: uuid }, (err, prefab) => {\r\n                    if (err) {\r\n                        console.error(err);\r\n                        return;\r\n                    }\r\n                    this._bulletPrefab = prefab;\r\n                    const bullet = this.instantiateBullet();\r\n                    if (!bullet) return;\r\n\r\n                    BulletSystem.onCreateBullet(this, bullet);\r\n                    // Set bullet position relative to emitter\r\n                    const emitterPos = this.node.getWorldPosition();\r\n                    bullet.node.setWorldPosition(\r\n                        emitterPos.x + position.x,\r\n                        emitterPos.y + position.y,\r\n                        emitterPos.z\r\n                    );\r\n                    bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n                    bullet.prop.speed.value *= this.emitPower.value;\r\n                    bullet.onReady();\r\n                });\r\n            });\r\n    }\r\n\r\n    protected instantiateBullet(): Bullet | null {\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this._bulletPrefab!);\r\n        if (!bulletNode) {\r\n            console.error(\"Emitter: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(Bullet);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        return bullet;\r\n    }\r\n\r\n    playEffect(prefab: Prefab, position: Vec3, rotation: Quat, duration: number) {\r\n        if (!prefab) return;\r\n\r\n        const effectNode = ObjectPool.getNode(this.node, prefab);\r\n        if (!effectNode) return;\r\n\r\n        effectNode.setWorldPosition(position);\r\n        effectNode.setWorldRotation(rotation);\r\n        // Play the effect and destroy it after duration\r\n        // effectNode.getComponent(ParticleSystem)?.play();\r\n        this.scheduleOnce(() => {\r\n            ObjectPool.returnNode(effectNode);\r\n        }, duration);\r\n    }\r\n\r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen(): boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this.isActive || !this.isActive.value) {\r\n            return;\r\n        }\r\n\r\n        switch (this._status) {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting();\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n\r\n        this.elapsedTime.value += deltaTime;\r\n        this._statusElapsedTime += deltaTime;\r\n        this._totalElapsedTime += deltaTime;\r\n\r\n        this.notifyAll();\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this._statusElapsedTime >= this.initialDelay.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.isPreWarm.value)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this._statusElapsedTime >= this.preWarmDuration.value) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting() {\r\n        if (this._statusElapsedTime > this.emitDuration.value) {\r\n            this.stopEmitting();\r\n            if (this.isLoop)\r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else\r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n\r\n        // Start emitting if not already started\r\n        if (!this._isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this._statusElapsedTime >= this._nextEmitTime) {\r\n            this.tryEmit();\r\n            if (this.perEmitInterval.value <= 0) {\r\n                this.scheduleNextEmit();\r\n            }\r\n            else {\r\n                // 开始这一波\r\n                this._nextEmitTime = this._statusElapsedTime + 10000000;\r\n            }\r\n        }\r\n\r\n        let wasEmitting = this._perEmitBulletQueue.length > 0;\r\n        // Process per-emit bullet queue based on precise timing\r\n        this.processPerEmitQueue();\r\n        if (wasEmitting && this._perEmitBulletQueue.length <= 0) {\r\n            this.scheduleNextEmit();\r\n        }\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this._statusElapsedTime >= this.loopInterval.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n        this.isActive.value = false;\r\n        this.isActive.notify();\r\n    }\r\n}\r\n"]}