System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, LevelDataEventTrigger, _crd, LevelDataEventTriggerType;

  _export("LevelDataEventTrigger", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "67a790bX21Daa1mdo8cX0Su", "LevelDataEventTrigger", undefined);

      _export("LevelDataEventTriggerType", LevelDataEventTriggerType = /*#__PURE__*/function (LevelDataEventTriggerType) {
        LevelDataEventTriggerType[LevelDataEventTriggerType["Log"] = 0] = "Log";
        LevelDataEventTriggerType[LevelDataEventTriggerType["Audio"] = 1] = "Audio";
        LevelDataEventTriggerType[LevelDataEventTriggerType["Wave"] = 2] = "Wave";
        return LevelDataEventTriggerType;
      }({}));

      _export("LevelDataEventTrigger", LevelDataEventTrigger = class LevelDataEventTrigger {
        constructor(type) {
          this._type = LevelDataEventTriggerType.Log;
          this._type = type;
        }

        onInit() {}

        fromJSON(obj) {}

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=212a75613812e337ef7e53ae87f2079ca2a98160.js.map