{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/IEventAction.ts"], "names": ["EventActionBase", "eTargetValueType", "eWrapMode", "Easing", "EDITOR", "eEventActionStatus", "constructor", "data", "_status", "Ready", "_elapsedTime", "_startValue", "_targetValue", "_delay", "_transitionDuration", "_duration", "isCompleted", "Completed", "canLerp", "onLoad", "context", "delay", "eval", "duration", "transitionDuration", "resetStartValue", "resetTargetValue", "name", "console", "log", "onStart", "Running", "onExecute", "dt", "onComplete", "onExecuteInternal", "lerp<PERSON><PERSON>ue", "startValue", "targetValue", "progress", "wrapMode", "Once", "Loop", "<PERSON><PERSON><PERSON>", "cycle", "Math", "floor", "localProgress", "lerp", "easing", "targetValueType", "Relative", "value"], "mappings": ";;;2EAuBaA,e;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAtBJC,MAAAA,gB,iBAAAA,gB;AAAkBC,MAAAA,S,iBAAAA,S;;AAElBC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,M,UAAAA,M;;;;;;;oCAeGC,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB;;;iCAICL,e,GAAN,MAAMA,eAAN,CAA8C;AAcjDM,QAAAA,WAAW,CAACC,IAAD,EAAyB;AAAA,eAb3BA,IAa2B;AAAA,eAX1BC,OAW0B,GAXIH,kBAAkB,CAACI,KAWvB;AAAA,eAV1BC,YAU0B,GAVH,CAUG;AAAA,eAT1BC,WAS0B,GATJ,CASI;AAAA,eAR1BC,YAQ0B,GARH,CAQG;AAAA,eAP1BC,MAO0B,GAPT,CAOS;AANpC;AACA;AACA;AAIoC,eAH1BC,mBAG0B,GAHI,CAGJ;AAAA,eAF1BC,SAE0B,GAFN,CAEM;AAChC,eAAKR,IAAL,GAAYA,IAAZ;AACH;;AAEDS,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAKR,OAAL,KAAiBH,kBAAkB,CAACY,SAA3C;AACH;;AAEDC,QAAAA,OAAO,GAAY;AACf,iBAAO,IAAP;AACH;;AAEDC,QAAAA,MAAM,CAACC,OAAD,EAAoC;AACtC,eAAKZ,OAAL,GAAeH,kBAAkB,CAACI,KAAlC;AACA,eAAKC,YAAL,GAAoB,CAApB;AACA,eAAKG,MAAL,GAAc,KAAKN,IAAL,CAAUc,KAAV,CAAgBC,IAAhB,EAAd;AACA,eAAKP,SAAL,GAAiB,KAAKR,IAAL,CAAUgB,QAAV,CAAmBD,IAAnB,EAAjB;AACA,eAAKR,mBAAL,GAA2B,KAAKP,IAAL,CAAUiB,kBAAV,CAA6BF,IAA7B,EAA3B;AACA,eAAKG,eAAL,CAAqBL,OAArB;AACA,eAAKM,gBAAL,CAAsBN,OAAtB;;AAEA,cAAIhB,MAAM,IAAI,KAAKG,IAAL,CAAUoB,IAAV,IAAkB,EAAhC,EAAoC;AAChCC,YAAAA,OAAO,CAACC,GAAR,0BAAmC,KAAKtB,IAAL,CAAUoB,IAA7C;AACH;AACJ;;AAEDG,QAAAA,OAAO,CAACV,OAAD,EAAoC;AACvC,eAAKZ,OAAL,GAAeH,kBAAkB,CAAC0B,OAAlC;AACH;;AAEDC,QAAAA,SAAS,CAACZ,OAAD,EAA8Ba,EAA9B,EAAgD;AACrD,eAAKvB,YAAL,IAAqBuB,EAArB;;AAEA,kBAAQ,KAAKzB,OAAb;AACI,iBAAKH,kBAAkB,CAACI,KAAxB;AACI,mBAAKqB,OAAL,CAAaV,OAAb;AACA;;AACJ,iBAAKf,kBAAkB,CAAC0B,OAAxB;AACI,kBAAI,KAAKrB,YAAL,GAAoB,KAAKG,MAA7B,EAAqC;AACjC;AACH;;AAED,kBAAI,KAAKH,YAAL,IAAqB,KAAKK,SAA9B,EAAyC;AACrC,qBAAKmB,UAAL,CAAgBd,OAAhB;AACH,eAFD,MAGK,IAAI,KAAKF,OAAL,EAAJ,EAAoB;AACrB,qBAAKiB,iBAAL,CAAuBf,OAAvB,EAAgC,KAAKgB,SAAL,CAAe,KAAKzB,WAApB,EAAiC,KAAKC,YAAtC,CAAhC;AACH;;AACD;;AACJ,iBAAKP,kBAAkB,CAACY,SAAxB;AACI;AAjBR;AAmBH;;AAEDiB,QAAAA,UAAU,CAACd,OAAD,EAAoC;AAC1C,eAAKe,iBAAL,CAAuBf,OAAvB,EAAgC,KAAKR,YAArC;AACA,eAAKJ,OAAL,GAAeH,kBAAkB,CAACY,SAAlC;AACH;;AAEDmB,QAAAA,SAAS,CAACC,UAAD,EAAqBC,WAArB,EAAkD;AACvD,cAAI,KAAKxB,mBAAL,IAA4B,CAAhC,EAAmC;AAC/B,mBAAOwB,WAAP;AACH;;AAED,cAAIC,QAAQ,GAAG,KAAK7B,YAAL,GAAoB,KAAKI,mBAAxC,CALuD,CAMvD;;AACA,cAAIyB,QAAQ,GAAG,GAAf,EAAoB;AAChB,oBAAQ,KAAKhC,IAAL,CAAUiC,QAAlB;AACI,mBAAK;AAAA;AAAA,0CAAUC,IAAf;AACIF,gBAAAA,QAAQ,GAAG,GAAX;AACA;;AACJ,mBAAK;AAAA;AAAA,0CAAUG,IAAf;AACIH,gBAAAA,QAAQ,GAAGA,QAAQ,GAAG,GAAtB;AACA;;AACJ,mBAAK;AAAA;AAAA,0CAAUI,QAAf;AACI,oBAAMC,KAAK,GAAGC,IAAI,CAACC,KAAL,CAAWP,QAAX,CAAd;AACA,oBAAMQ,aAAa,GAAGR,QAAQ,GAAG,GAAjC;AACAA,gBAAAA,QAAQ,GAAIK,KAAK,GAAG,CAAR,KAAc,CAAf,GAAoBG,aAApB,GAAqC,MAAMA,aAAtD;AACA;AAXR;AAaH;;AAED,iBAAO;AAAA;AAAA,gCAAOC,IAAP,CAAY,KAAKzC,IAAL,CAAU0C,MAAtB,EAA8BZ,UAA9B,EAA0CC,WAA1C,EAAuDC,QAAvD,CAAP;AACH,SAjGgD,CAmGjD;;;AACUd,QAAAA,eAAe,CAACL,OAAD,EAAoC;AACzD,eAAKT,WAAL,GAAmB,CAAnB;AACH;;AAESe,QAAAA,gBAAgB,CAACN,OAAD,EAAoC;AAC1D,kBAAQ,KAAKb,IAAL,CAAU2C,eAAlB;AAEI,iBAAK;AAAA;AAAA,sDAAiBC,QAAtB;AACI,mBAAKvC,YAAL,GAAoB,KAAKL,IAAL,CAAU+B,WAAV,CAAsBhB,IAAtB,KAA+B,KAAKX,WAAxD;AACA;;AACJ;AACI,mBAAKC,YAAL,GAAoB,KAAKL,IAAL,CAAU+B,WAAV,CAAsBhB,IAAtB,EAApB;AACA;AAPR;AASH;;AAESa,QAAAA,iBAAiB,CAACf,OAAD,EAA8BgC,KAA9B,EAAmD,CAC1E;AACH;;AAtHgD,O", "sourcesContent": ["\r\nimport { eTargetValueType, eWrapMode, IEventActionData } from \"../../data/bullet/EventGroupData\";\r\nimport { IEventGroupContext, EventGroupContext } from \"../EventGroup\";\r\nimport { Easing} from \"../Easing\";\r\nimport { EDITOR } from \"cc/env\";\r\n\r\nexport interface IEventAction {\r\n    readonly data: IEventActionData;\r\n\r\n    isCompleted(): boolean;\r\n\r\n    onLoad(context: IEventGroupContext): void;\r\n\r\n    onStart(context: IEventGroupContext): void;\r\n    onExecute(context: IEventGroupContext, dt: number): void;\r\n    onComplete(context: IEventGroupContext): void;\r\n    // onCancel? \r\n}\r\n\r\nexport enum eEventActionStatus {\r\n    Ready = 0, Running, Completed\r\n}\r\n\r\nexport class EventActionBase implements IEventAction {\r\n    readonly data: IEventActionData;\r\n\r\n    protected _status: eEventActionStatus = eEventActionStatus.Ready;\r\n    protected _elapsedTime: number = 0;\r\n    protected _startValue: number = 0;\r\n    protected _targetValue: number = 0;\r\n    protected _delay: number = 0;\r\n    // 这里有两个时间：\r\n    // _transitionDuration是从_startValue->_targetValue所需要的时间\r\n    // _duration是整个action执行的生命周期\r\n    protected _transitionDuration: number = 0;\r\n    protected _duration: number = 0;\r\n\r\n    constructor(data: IEventActionData) {\r\n        this.data = data;\r\n    }\r\n\r\n    isCompleted(): boolean {\r\n        return this._status === eEventActionStatus.Completed;\r\n    }\r\n\r\n    canLerp(): boolean {\r\n        return true;\r\n    }\r\n\r\n    onLoad(context: IEventGroupContext): void {\r\n        this._status = eEventActionStatus.Ready;\r\n        this._elapsedTime = 0;\r\n        this._delay = this.data.delay.eval();\r\n        this._duration = this.data.duration.eval();\r\n        this._transitionDuration = this.data.transitionDuration.eval();\r\n        this.resetStartValue(context);\r\n        this.resetTargetValue(context);\r\n\r\n        if (EDITOR && this.data.name != \"\") {\r\n            console.log(`EventAction:onLoad: ${this.data.name}`);\r\n        }\r\n    }\r\n\r\n    onStart(context: IEventGroupContext): void {\r\n        this._status = eEventActionStatus.Running;\r\n    }\r\n\r\n    onExecute(context: IEventGroupContext, dt: number): void {\r\n        this._elapsedTime += dt;\r\n        \r\n        switch (this._status) {\r\n            case eEventActionStatus.Ready:\r\n                this.onStart(context);\r\n                break;\r\n            case eEventActionStatus.Running:\r\n                if (this._elapsedTime < this._delay) {\r\n                    return;\r\n                }\r\n\r\n                if (this._elapsedTime >= this._duration) {\r\n                    this.onComplete(context);\r\n                }\r\n                else if (this.canLerp()) {\r\n                    this.onExecuteInternal(context, this.lerpValue(this._startValue, this._targetValue));\r\n                }\r\n                break;\r\n            case eEventActionStatus.Completed:\r\n                return;\r\n        }\r\n    }\r\n\r\n    onComplete(context: IEventGroupContext): void {\r\n        this.onExecuteInternal(context, this._targetValue);\r\n        this._status = eEventActionStatus.Completed;\r\n    }\r\n\r\n    lerpValue(startValue: number, targetValue: number): number {\r\n        if (this._transitionDuration <= 0) {\r\n            return targetValue;\r\n        }\r\n\r\n        let progress = this._elapsedTime / this._transitionDuration;\r\n        // Handle wrap modes when transition duration is exceeded\r\n        if (progress > 1.0) {\r\n            switch (this.data.wrapMode) {\r\n                case eWrapMode.Once:\r\n                    progress = 1.0;\r\n                    break;\r\n                case eWrapMode.Loop:\r\n                    progress = progress % 1.0;\r\n                    break;\r\n                case eWrapMode.Pingpong:\r\n                    const cycle = Math.floor(progress);\r\n                    const localProgress = progress % 1.0;\r\n                    progress = (cycle % 2 === 0) ? localProgress : (1.0 - localProgress);\r\n                    break;\r\n            }\r\n        }\r\n\r\n        return Easing.lerp(this.data.easing, startValue, targetValue, progress);\r\n    }\r\n\r\n    // override this to get the correct start value\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = 0;\r\n    }\r\n\r\n    protected resetTargetValue(context: IEventGroupContext): void {\r\n        switch (this.data.targetValueType)\r\n        {\r\n            case eTargetValueType.Relative:\r\n                this._targetValue = this.data.targetValue.eval() + this._startValue;\r\n                break;\r\n            default:\r\n                this._targetValue = this.data.targetValue.eval();\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        // Default implementation does nothing\r\n    }\r\n}\r\n"]}