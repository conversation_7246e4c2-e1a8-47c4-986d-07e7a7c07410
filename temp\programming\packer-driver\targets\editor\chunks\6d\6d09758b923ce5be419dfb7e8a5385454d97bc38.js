System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, EventActionBase, eEmitterProp, ePropMask, EmitterActionBase, EmitterActionBase_BoolModifier, EmitterActionBase_NumberModifier, EmitterAction_Active, EmitterAction_Prewarm, EmitterAction_InitialDelay, EmitterAction_PrewarmDuration, EmitterAction_Duration, EmitterAction_ElapsedTime, EmitterAction_Loop, EmitterAction_LoopInterval, EmitterAction_EmitInterval, EmitterAction_PerEmitCount, EmitterAction_PerEmitInterval, EmitterAction_PerEmitOffsetX, EmitterAction_Angle, EmitterAction_Count, EmitterAction_BulletDuration, EmitterAction_BulletDamage, EmitterAction_BulletSpeed, EmitterAction_BulletSpeedAngle, EmitterAction_BulletAcceleration, EmitterAction_BulletAccelerationAngle, EmitterAction_BulletScale, EmitterAction_BulletColorR, EmitterAction_BulletColorG, EmitterAction_BulletColorB, EmitterAction_BulletFacingMoveDir, EmitterAction_BulletTrackingTarget, EmitterAction_BulletDestructive, EmitterAction_BulletDestructiveOnHit, _crd;

  function _reportPossibleCrUseOfEventActionBase(extras) {
    _reporterNs.report("EventActionBase", "./IEventAction", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventGroupContext(extras) {
    _reporterNs.report("IEventGroupContext", "../EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterProp(extras) {
    _reporterNs.report("eEmitterProp", "../Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfePropMask(extras) {
    _reporterNs.report("ePropMask", "../Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfProperty(extras) {
    _reporterNs.report("Property", "../PropertyContainer", _context.meta, extras);
  }

  _export({
    EmitterActionBase: void 0,
    EmitterActionBase_BoolModifier: void 0,
    EmitterActionBase_NumberModifier: void 0,
    EmitterAction_Active: void 0,
    EmitterAction_Prewarm: void 0,
    EmitterAction_InitialDelay: void 0,
    EmitterAction_PrewarmDuration: void 0,
    EmitterAction_Duration: void 0,
    EmitterAction_ElapsedTime: void 0,
    EmitterAction_Loop: void 0,
    EmitterAction_LoopInterval: void 0,
    EmitterAction_EmitInterval: void 0,
    EmitterAction_PerEmitCount: void 0,
    EmitterAction_PerEmitInterval: void 0,
    EmitterAction_PerEmitOffsetX: void 0,
    EmitterAction_Angle: void 0,
    EmitterAction_Count: void 0,
    EmitterAction_BulletDuration: void 0,
    EmitterAction_BulletDamage: void 0,
    EmitterAction_BulletSpeed: void 0,
    EmitterAction_BulletSpeedAngle: void 0,
    EmitterAction_BulletAcceleration: void 0,
    EmitterAction_BulletAccelerationAngle: void 0,
    EmitterAction_BulletScale: void 0,
    EmitterAction_BulletColorR: void 0,
    EmitterAction_BulletColorG: void 0,
    EmitterAction_BulletColorB: void 0,
    EmitterAction_BulletFacingMoveDir: void 0,
    EmitterAction_BulletTrackingTarget: void 0,
    EmitterAction_BulletDestructive: void 0,
    EmitterAction_BulletDestructiveOnHit: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      EventActionBase = _unresolved_2.EventActionBase;
    }, function (_unresolved_3) {
      eEmitterProp = _unresolved_3.eEmitterProp;
      ePropMask = _unresolved_3.ePropMask;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7cf3e5fqPZK4K903tZmgBYZ", "EmitterEventActions", undefined);

      _export("EmitterActionBase", EmitterActionBase = class EmitterActionBase extends (_crd && EventActionBase === void 0 ? (_reportPossibleCrUseOfEventActionBase({
        error: Error()
      }), EventActionBase) : EventActionBase) {// this was intentionally left blank
      });

      _export("EmitterActionBase_BoolModifier", EmitterActionBase_BoolModifier = class EmitterActionBase_BoolModifier extends EmitterActionBase {
        constructor(...args) {
          super(...args);
          this._targetProperty = undefined;
        }

        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).IsActive;
        }

        onLoad(context) {
          this._targetProperty = context.emitter.getProperty(this.propertyType);
          super.onLoad(context);
        }

        onStart(context) {
          this._targetProperty.addWriteMask((_crd && ePropMask === void 0 ? (_reportPossibleCrUseOfePropMask({
            error: Error()
          }), ePropMask) : ePropMask).EventGroup);

          super.onStart(context);
        }

        onComplete(context) {
          this._targetProperty.removeWriteMask((_crd && ePropMask === void 0 ? (_reportPossibleCrUseOfePropMask({
            error: Error()
          }), ePropMask) : ePropMask).EventGroup);

          super.onComplete(context);
        }

        canLerp() {
          return false;
        }

        resetStartValue(context) {
          this._startValue = this._targetProperty.value ? 1 : 0;
        }

        onExecuteInternal(context, value) {
          this._targetProperty.setValue(value === 1, (_crd && ePropMask === void 0 ? (_reportPossibleCrUseOfePropMask({
            error: Error()
          }), ePropMask) : ePropMask).EventGroup);
        }

      });

      _export("EmitterActionBase_NumberModifier", EmitterActionBase_NumberModifier = class EmitterActionBase_NumberModifier extends EmitterActionBase {
        constructor(...args) {
          super(...args);
          this._targetProperty = undefined;
        }

        get propertyType() {
          // override this
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).InitialDelay;
        }

        onLoad(context) {
          this._targetProperty = context.emitter.getProperty(this.propertyType);
          super.onLoad(context);
        }

        onStart(context) {
          this._targetProperty.addWriteMask((_crd && ePropMask === void 0 ? (_reportPossibleCrUseOfePropMask({
            error: Error()
          }), ePropMask) : ePropMask).EventGroup);

          super.onStart(context);
        }

        onComplete(context) {
          this._targetProperty.removeWriteMask((_crd && ePropMask === void 0 ? (_reportPossibleCrUseOfePropMask({
            error: Error()
          }), ePropMask) : ePropMask).EventGroup);

          super.onComplete(context);
        }

        resetStartValue(context) {
          this._startValue = this._targetProperty.value;
        }

        onExecuteInternal(context, value) {
          this._targetProperty.setValue(value, (_crd && ePropMask === void 0 ? (_reportPossibleCrUseOfePropMask({
            error: Error()
          }), ePropMask) : ePropMask).EventGroup);
        }

      }); // 修改发射器启用状态


      _export("EmitterAction_Active", EmitterAction_Active = class EmitterAction_Active extends EmitterActionBase_BoolModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).IsActive;
        }

      });

      _export("EmitterAction_Prewarm", EmitterAction_Prewarm = class EmitterAction_Prewarm extends EmitterActionBase_BoolModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).IsPreWarm;
        }

      }); // 修改发射器初始延迟时间


      _export("EmitterAction_InitialDelay", EmitterAction_InitialDelay = class EmitterAction_InitialDelay extends EmitterActionBase_NumberModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).InitialDelay;
        }

      });

      _export("EmitterAction_PrewarmDuration", EmitterAction_PrewarmDuration = class EmitterAction_PrewarmDuration extends EmitterActionBase_NumberModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).PrewarmDuration;
        }

      }); // 修改发射器持续时间


      _export("EmitterAction_Duration", EmitterAction_Duration = class EmitterAction_Duration extends EmitterActionBase_NumberModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).EmitDuration;
        }

      }); // 修改发射器已运行时间


      _export("EmitterAction_ElapsedTime", EmitterAction_ElapsedTime = class EmitterAction_ElapsedTime extends EmitterActionBase_NumberModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).ElapsedTime;
        }

      }); // 修改发射器是否循环(boolean)


      _export("EmitterAction_Loop", EmitterAction_Loop = class EmitterAction_Loop extends EmitterActionBase_BoolModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).IsLoop;
        }

      }); // 循环间隔


      _export("EmitterAction_LoopInterval", EmitterAction_LoopInterval = class EmitterAction_LoopInterval extends EmitterActionBase_NumberModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).LoopInterval;
        }

      });

      _export("EmitterAction_EmitInterval", EmitterAction_EmitInterval = class EmitterAction_EmitInterval extends EmitterActionBase_NumberModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).EmitInterval;
        }

      });

      _export("EmitterAction_PerEmitCount", EmitterAction_PerEmitCount = class EmitterAction_PerEmitCount extends EmitterActionBase_NumberModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).PerEmitCount;
        }

      });

      _export("EmitterAction_PerEmitInterval", EmitterAction_PerEmitInterval = class EmitterAction_PerEmitInterval extends EmitterActionBase_NumberModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).PerEmitInterval;
        }

      });

      _export("EmitterAction_PerEmitOffsetX", EmitterAction_PerEmitOffsetX = class EmitterAction_PerEmitOffsetX extends EmitterActionBase_NumberModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).PerEmitOffsetX;
        }

      });

      _export("EmitterAction_Angle", EmitterAction_Angle = class EmitterAction_Angle extends EmitterActionBase_NumberModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).Angle;
        }

      });

      _export("EmitterAction_Count", EmitterAction_Count = class EmitterAction_Count extends EmitterActionBase_NumberModifier {
        get propertyType() {
          return (_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).Count;
        }

      }); // 以下是发射器修改子弹属性的部分


      _export("EmitterAction_BulletDuration", EmitterAction_BulletDuration = class EmitterAction_BulletDuration extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.duration.value;
        }

        onExecuteInternal(context, value) {
          context.emitter.bulletProp.duration.value = value;
        }

      });

      _export("EmitterAction_BulletDamage", EmitterAction_BulletDamage = class EmitterAction_BulletDamage extends EmitterActionBase {
        resetStartValue(context) {// this._startValue = context.emitter!.bulletProp.damage.value;
        }

        onExecuteInternal(context, value) {// context.emitter!.bulletProp.damage.value = value;
        }

      });

      _export("EmitterAction_BulletSpeed", EmitterAction_BulletSpeed = class EmitterAction_BulletSpeed extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.speed.value;
        }

        onExecuteInternal(context, value) {
          context.emitter.bulletProp.speed.value = value;
        }

      });

      _export("EmitterAction_BulletSpeedAngle", EmitterAction_BulletSpeedAngle = class EmitterAction_BulletSpeedAngle extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.speedAngle.value;
        }

        onExecuteInternal(context, value) {
          context.emitter.bulletProp.speedAngle.value = value;
        }

      });

      _export("EmitterAction_BulletAcceleration", EmitterAction_BulletAcceleration = class EmitterAction_BulletAcceleration extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.acceleration.value;
        }

        onExecuteInternal(context, value) {
          context.emitter.bulletProp.acceleration.value = value;
        }

      });

      _export("EmitterAction_BulletAccelerationAngle", EmitterAction_BulletAccelerationAngle = class EmitterAction_BulletAccelerationAngle extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.accelerationAngle.value;
        }

        onExecuteInternal(context, value) {
          context.emitter.bulletProp.accelerationAngle.value = value;
        }

      });

      _export("EmitterAction_BulletScale", EmitterAction_BulletScale = class EmitterAction_BulletScale extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.scale.value;
        }

        onExecuteInternal(context, value) {
          context.emitter.bulletProp.scale.value = value;
        }

      });

      _export("EmitterAction_BulletColorR", EmitterAction_BulletColorR = class EmitterAction_BulletColorR extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.color.value.r;
        }

        onExecuteInternal(context, value) {
          let color = context.emitter.bulletProp.color.value;
          color.r = value;
          context.emitter.bulletProp.color.value = color;
        }

      });

      _export("EmitterAction_BulletColorG", EmitterAction_BulletColorG = class EmitterAction_BulletColorG extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.color.value.g;
        }

        onExecuteInternal(context, value) {
          let color = context.emitter.bulletProp.color.value;
          color.g = value;
          context.emitter.bulletProp.color.value = color;
        }

      });

      _export("EmitterAction_BulletColorB", EmitterAction_BulletColorB = class EmitterAction_BulletColorB extends EmitterActionBase {
        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.color.value.b;
        }

        onExecuteInternal(context, value) {
          let color = context.emitter.bulletProp.color.value;
          color.b = value;
          context.emitter.bulletProp.color.value = color;
        }

      });

      _export("EmitterAction_BulletFacingMoveDir", EmitterAction_BulletFacingMoveDir = class EmitterAction_BulletFacingMoveDir extends EmitterActionBase {
        canLerp() {
          return false;
        }

        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.isFacingMoveDir.value ? 1 : 0;
        }

        onExecuteInternal(context, value) {
          context.emitter.bulletProp.isFacingMoveDir.value = value === 1;
        }

      });

      _export("EmitterAction_BulletTrackingTarget", EmitterAction_BulletTrackingTarget = class EmitterAction_BulletTrackingTarget extends EmitterActionBase {
        canLerp() {
          return false;
        }

        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.isTrackingTarget.value ? 1 : 0;
        }

        onExecuteInternal(context, value) {
          context.emitter.bulletProp.isTrackingTarget.value = value === 1;
        }

      });

      _export("EmitterAction_BulletDestructive", EmitterAction_BulletDestructive = class EmitterAction_BulletDestructive extends EmitterActionBase {
        canLerp() {
          return false;
        }

        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.isDestructive.value ? 1 : 0;
        }

        onExecuteInternal(context, value) {
          context.emitter.bulletProp.isDestructive.value = value === 1;
        }

      });

      _export("EmitterAction_BulletDestructiveOnHit", EmitterAction_BulletDestructiveOnHit = class EmitterAction_BulletDestructiveOnHit extends EmitterActionBase {
        canLerp() {
          return false;
        }

        resetStartValue(context) {
          this._startValue = context.emitter.bulletProp.isDestructiveOnHit.value ? 1 : 0;
        }

        onExecuteInternal(context, value) {
          context.emitter.bulletProp.isDestructiveOnHit.value = value === 1;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6d09758b923ce5be419dfb7e8a5385454d97bc38.js.map