{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger.ts"], "names": ["LevelDataEventTrigger", "LevelDataEventTriggerType", "constructor", "type", "_type", "Log", "onInit", "fromJSON", "obj"], "mappings": ";;;iBAMaA,qB;;;;;;;;;;;;;2CANDC,yB,0BAAAA,yB;AAAAA,QAAAA,yB,CAAAA,yB;AAAAA,QAAAA,yB,CAAAA,yB;AAAAA,QAAAA,yB,CAAAA,yB;eAAAA,yB;;;uCAMCD,qB,GAAN,MAAMA,qBAAN,CAA4B;AAE/BE,QAAAA,WAAW,CAACC,IAAD,EAAmC;AAAA,eADvCC,KACuC,GADJH,yBAAyB,CAACI,GACtB;AAC1C,eAAKD,KAAL,GAAaD,IAAb;AACH;;AAEMG,QAAAA,MAAM,GAAG,CAAE;;AAEXC,QAAAA,QAAQ,CAACC,GAAD,EAAW,CAAE;;AARG,O", "sourcesContent": ["export enum LevelDataEventTriggerType {\r\n    Log = 0,\r\n    Audio = 1,\r\n    Wave = 2,\r\n}\r\n\r\nexport class LevelDataEventTrigger {\r\n    public _type: LevelDataEventTriggerType = LevelDataEventTriggerType.Log;\r\n    constructor(type : LevelDataEventTriggerType) {\r\n        this._type = type;\r\n    }\r\n\r\n    public onInit() {}\r\n\r\n    public fromJSON(obj: any) {}\r\n}"]}