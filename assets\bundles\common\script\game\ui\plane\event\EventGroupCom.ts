import Entity from "db://assets/bundles/common/script/game/ui/base/Entity";
import BaseComp from "db://assets/bundles/common/script/game/ui/base/BaseComp";
import EnemyPlaneBase from "db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase";
import { IEventGroupContext } from "../../../bullet/EventGroup";

export class ComContext implements IEventGroupContext {
    emitter: null = null;
    bullet: null = null;
    playerPlane: null = null;

    plane: EnemyPlaneBase|null = null;

    reset() {
        this.emitter = null;
        this.bullet = null;
        this.playerPlane = null;
        this.plane = null;
    }
}

// 挂在Entity身上，用来执行事件组的组件
export class EventGroupComp extends BaseComp {
    plane: EnemyPlaneBase|null = null;

    private _context: ComContext = new ComContext();

    init(entity: Entity) {
        super.init(entity);
        this.plane = entity as EnemyPlaneBase;
        this._context.plane = this.plane;
    }

    updateGameLogic(dt: number) {

    }
}