{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/WaveManager.ts"], "names": ["WaveManager", "SingletonBase", "GameIns", "enemyCreateAble", "_bEnemyCreateAble", "value", "constructor", "_waveNorDatasMap", "Map", "_bEnemyNorCreateAble", "_activeWaves", "_boss<PERSON><PERSON><PERSON><PERSON><PERSON>", "_bossCreateTime", "_bossToAddArr", "_bShowBossWarning", "reset", "setEnemyActions", "actions", "gameStart", "getNorWaveDatas", "groupID", "get", "addWaveByLevel", "wave", "posX", "posY", "trigger", "push", "updateGameLogic", "deltaTime", "_updateWaves", "_updateBoss", "dtInMiliseconds", "i", "length", "tick", "isCompleted", "splice", "console", "log", "enemyManager", "isEnemyOver", "bossData", "boss<PERSON><PERSON><PERSON>", "addBoss", "enemyNorIDs", "battleManager", "bossWillEnter"], "mappings": ";;;uGAUqBA,W;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATZC,MAAAA,a,iBAAAA,a;;AAIAC,MAAAA,O,iBAAAA,O;;;;;;;;;yBAKYF,W,GAAN,MAAMA,WAAN;AAAA;AAAA,0CAAqD;AAe7C,YAAfG,eAAe,GAAY;AAC3B,iBAAO,KAAKC,iBAAZ;AACH;;AAEkB,YAAfD,eAAe,CAACE,KAAD,EAAiB;AAChC,eAAKD,iBAAL,GAAyBC,KAAzB;AACH;;AAEDC,QAAAA,WAAW,GAAG;AACV,kBADU,CAEV;;AAFU,eAtBNC,gBAsBM,GAtBuC,IAAIC,GAAJ,EAsBvC;AAtBiD;AAsBjD,eApBNJ,iBAoBM,GApBuB,KAoBvB;AApB6B;AAoB7B,eAnBNK,oBAmBM,GAnB0B,KAmB1B;AAnBgC;AAE9C;AAiBc,eAhBNC,YAgBM,GAhBiB,EAgBjB;AAdd;AAcc,eAbNC,gBAaM,GAbqB,CAarB;AAAA,eAZNC,eAYM,GAZoB,CAYpB;AAAA,eAXNC,aAWM,GAXiB,EAWjB;AAAA,eAVNC,iBAUM,GAVuB,KAUvB;AAGb;;AAEDC,QAAAA,KAAK,GAAS;AACV,eAAKX,iBAAL,GAAyB,KAAzB;AACA,eAAKK,oBAAL,GAA4B,KAA5B,CAFU,CAGV;AACA;AACA;AACA;;AACA,eAAKC,YAAL,GAAoB,EAApB;AACA,eAAKI,iBAAL,GAAyB,KAAzB;AACA,eAAKF,eAAL,GAAuB,CAAvB;AACH;;AAEDI,QAAAA,eAAe,CAACC,OAAD,EAA6B,CACxC;AACH;;AAEDC,QAAAA,SAAS,GAAS;AACd,eAAKd,iBAAL,GAAyB,IAAzB;AACA,eAAKK,oBAAL,GAA4B,IAA5B,CAFc,CAGd;AACA;AACA;AACA;AAEA;AACA;AACH;;AAEDU,QAAAA,eAAe,CAACC,OAAD,EAA2C;AACtD,iBAAO,KAAKb,gBAAL,CAAsBc,GAAtB,CAA0BD,OAA1B,CAAP;AACH;;AAEDE,QAAAA,cAAc,CAACC,IAAD,EAAaC,IAAb,EAA2BC,IAA3B,EAA+C;AACzD;AACA,cAAIF,IAAJ,EAAU;AACNA,YAAAA,IAAI,CAACG,OAAL,CAAaF,IAAb,EAAmBC,IAAnB;;AACA,iBAAKf,YAAL,CAAkBiB,IAAlB,CAAuBJ,IAAvB;AACH;AACJ,SAlE+D,CAoEhE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEqB,cAAfK,eAAe,CAACC,SAAD,EAAoB;AACrC;AACA,eAAKC,YAAL,CAAkBD,SAAlB;;AACA,eAAKE,WAAL,CAAiBF,SAAjB;AACH;AAED;AACJ;AACA;AACA;AACI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;AACA;AACI;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEQC,QAAAA,YAAY,CAACD,SAAD,EAAoB;AACpC,gBAAMG,eAAe,GAAGH,SAAS,GAAG,IAApC;;AACA,eAAK,IAAII,CAAC,GAAG,KAAKvB,YAAL,CAAkBwB,MAAlB,GAA2B,CAAxC,EAA2CD,CAAC,IAAI,CAAhD,EAAmDA,CAAC,EAApD,EAAwD;AACpD,kBAAMV,IAAI,GAAG,KAAKb,YAAL,CAAkBuB,CAAlB,CAAb;AACAV,YAAAA,IAAI,CAACY,IAAL,CAAUH,eAAV;;AACA,gBAAIT,IAAI,CAACa,WAAT,EAAsB;AAClB,mBAAK1B,YAAL,CAAkB2B,MAAlB,CAAyBJ,CAAzB,EAA4B,CAA5B;AACH;AACJ;;AAEDK,UAAAA,OAAO,CAACC,GAAR,CAAa,iCAAgC,KAAK7B,YAAL,CAAkBwB,MAAO,EAAtE;AACH;AAED;AACJ;AACA;AACA;AACI;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;AACA;AACI;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;AAEA;AACJ;AACA;AACA;;;AACYH,QAAAA,WAAW,CAACF,SAAD,EAA0B;AACzC,cAAI,KAAKhB,aAAL,CAAmBqB,MAAnB,GAA4B,CAA5B,IAAiC;AAAA;AAAA,kCAAQM,YAAR,CAAqBC,WAArB,EAArC,EAAwE;AACpE,iBAAK7B,eAAL,IAAwBiB,SAAxB;;AACA,gBAAI,KAAKjB,eAAL,GAAuB,KAAKD,gBAAhC,EAAkD;AAC9C,oBAAM+B,QAAQ,GAAG,KAAK7B,aAAL,CAAmB,CAAnB,CAAjB;AACA;AAAA;AAAA,sCAAQ8B,WAAR,CAAoBC,OAApB,CAA4BF,QAAQ,CAACG,WAAT,CAAqB,CAArB,CAA5B;;AACA,mBAAKhC,aAAL,CAAmBwB,MAAnB,CAA0B,CAA1B,EAA6B,CAA7B;;AACA;AAAA;AAAA,sCAAQS,aAAR,CAAsBC,aAAtB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACIN,QAAAA,WAAW,GAAY;AACnB;AACA,iBAAO,KAAK/B,YAAL,CAAkBwB,MAAlB,IAA4B,CAA5B,IAAiC,KAAKrB,aAAL,CAAmBqB,MAAnB,KAA8B,CAAtE;AACH;;AA3Q+D,O", "sourcesContent": ["import { v2, warn } from \"cc\";\r\nimport { SingletonBase } from \"../../../../../scripts/core/base/SingletonBase\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport { EnemyWave } from \"../data/EnemyWave\";\r\nimport { StageData } from \"../data/StageData\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport EnemyPlane from \"../ui/plane/enemy/EnemyPlane\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { Wave } from \"../wave/Wave\";\r\n\r\nexport default class WaveManager extends SingletonBase<WaveManager> {\r\n    private _waveNorDatasMap: Map<number, EnemyWave[]> = new Map();// 波次配表数据\r\n\r\n    private _bEnemyCreateAble: boolean = false;//是否可以创建敌机\r\n    private _bEnemyNorCreateAble: boolean = false;//是否可以创建普通敌机\r\n\r\n    // private _waves: Map<string, Wave> = new Map();\r\n    private _activeWaves: Wave[] = [];\r\n\r\n    //boss\r\n    private _bossCreateDelay: number = 0;\r\n    private _bossCreateTime: number = 0;\r\n    private _bossToAddArr: any[] = [];\r\n    private _bShowBossWarning: boolean = false;\r\n\r\n    get enemyCreateAble(): boolean {\r\n        return this._bEnemyCreateAble;\r\n    }\r\n\r\n    set enemyCreateAble(value: boolean) {\r\n        this._bEnemyCreateAble = value;\r\n    }\r\n\r\n    constructor() {\r\n        super();\r\n        // this.initConfig();\r\n    }\r\n\r\n    reset(): void {\r\n        this._bEnemyCreateAble = false;\r\n        this._bEnemyNorCreateAble = false;\r\n        // this._waveActionArr = [];\r\n        // this._waveNumArr = [];\r\n        // this._waveTimeArr = [];\r\n        // this._waves.clear();\r\n        this._activeWaves = [];\r\n        this._bShowBossWarning = false;\r\n        this._bossCreateTime = 0;\r\n    }\r\n\r\n    setEnemyActions(actions: StageData[]): void {\r\n        // this._enemyActions = actions;\r\n    }\r\n\r\n    gameStart(): void {\r\n        this._bEnemyCreateAble = true;\r\n        this._bEnemyNorCreateAble = true;\r\n        // this._waveArr = [];\r\n        // this._waveActionArr = [];\r\n        // this._waveNumArr = [];\r\n        // this._waveTimeArr = [];\r\n\r\n        // 这里不能清掉，清掉后存在问题: 时序上是先addWaveByLevel，后gameStart\r\n        // this._waves = [];\r\n    }\r\n\r\n    getNorWaveDatas(groupID: number): EnemyWave[] | undefined {\r\n        return this._waveNorDatasMap.get(groupID);\r\n    }\r\n\r\n    addWaveByLevel(wave: Wave, posX: number, posY: number): void {\r\n        // this._waves.set(wave.uuid, wave);\r\n        if (wave) {\r\n            wave.trigger(posX, posY);\r\n            this._activeWaves.push(wave);\r\n        }\r\n    }\r\n\r\n    // triggerWaves(waveUuid: string[], posX: number, posY: number) {\r\n    //     waveUuid.forEach((uuid) => {\r\n    //         const wave = this._waves.get(uuid);\r\n    //         if (wave) {\r\n    //             wave.trigger(posX, posY);\r\n    //             this._activeWaves.push(wave);\r\n    //         }\r\n    //     })\r\n    // }\r\n\r\n    async updateGameLogic(deltaTime: number) {\r\n        // this._updateCurAction(deltaTime);\r\n        this._updateWaves(deltaTime);\r\n        this._updateBoss(deltaTime);\r\n    }\r\n\r\n    /**\r\n     * 更新当前敌人行为\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    // private _updateCurAction(deltaTime: number): void {\r\n    //     if (!this._enemyOver) {\r\n    //         if (this._enemyActionIndex >= (this._enemyActions?.length || 0)) {\r\n    //             this._enemyOver = true;\r\n    //             warn(\"enemy over\");\r\n    //         } else if (this.enemyCreateAble && !this._curEnemyAction) {\r\n    //             const action = this._enemyActions![this._enemyActionIndex];\r\n    //             switch (action.type) {\r\n    //                 case 0:\r\n    //                     this._enemyCreateTime += deltaTime;\r\n    //                     if (\r\n    //                         this._enemyCreateTime >= action.enemyNorInterval ||\r\n    //                         (this._waveArr.length === 0 && GameIns.enemyManager.getNormalPlaneCount() === 0)\r\n    //                     ) {\r\n    //                         this._curEnemyAction = action;\r\n    //                     }\r\n    //                     break;\r\n    //                 default:\r\n    //                     if (action.type >= 100) {\r\n    //                         console.warn(\"Boss stage\", action.type, action.enemyNorIDs[0]);\r\n    //                         this._bossCreateDelay = action.enemyNorInterval;\r\n    //                         this._bossToAddArr.push(action);\r\n    //                         this._enemyActionIndex++;\r\n    //                     }\r\n    //             }\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    /**\r\n     * 更新敌人逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    // private async _updateEnemy(deltaTime: number) {\r\n    //     // await this._updateEnemyCreate(deltaTime);\r\n\r\n    //     if (this._curEnemyAction) {\r\n    //         if (!this._updateNorEnemys(deltaTime)) {\r\n    //             this._curEnemyAction = null;\r\n    //             this._enemyActionIndex++;\r\n    //             this._enemyCreateTime = 0;\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    private _updateWaves(deltaTime: number) {\r\n        const dtInMiliseconds = deltaTime * 1000;\r\n        for (let i = this._activeWaves.length - 1; i >= 0; i--) {\r\n            const wave = this._activeWaves[i];\r\n            wave.tick(dtInMiliseconds);\r\n            if (wave.isCompleted) {\r\n                this._activeWaves.splice(i, 1);\r\n            }\r\n        }\r\n\r\n        console.log(`ybgg _updateWaves left waves: ${this._activeWaves.length}`)\r\n    }\r\n\r\n    /**\r\n     * 更新敌人生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    // private async _updateEnemyCreate(deltaTime: number): Promise<void> {\r\n    //     for (let i = 0; i < this._waveArr.length; i++) {\r\n    //         const wave = this._waveArr[i];\r\n    //         this._waveTimeArr[i] += deltaTime;\r\n    //         const currentEnemyCount = this._waveNumArr[i];\r\n    //         let posX = GameConst.EnemyPos.x;\r\n    //         let posY = GameConst.EnemyPos.y;\r\n\r\n    //         if (wave.bSetStartPos) {\r\n    //             posX += wave.startPosX;\r\n    //             posY += wave.startPosY;\r\n    //         }\r\n\r\n    //         for (let j = currentEnemyCount; j < wave.enemyNum; j++) {\r\n    //             if (wave.enemyInterval * (j + 1) < this._waveTimeArr[i]) {\r\n    //                 this._waveNumArr[i]++;\r\n    //                 let enemy:EnemyPlane|null;\r\n    //                 const enemyPosX = posX + wave.posDX * (j + 1);\r\n    //                 const enemyPosY = posY + wave.posDY * (j + 1);\r\n\r\n    //                 switch (wave.type) {\r\n    //                     case 0:\r\n    //                         enemy = await GameIns.enemyManager.addPlane(wave.enemyID);\r\n    //                         if (enemy) {\r\n    //                             if (j < wave.firstShootDelay.length) {\r\n    //                                 enemy.setFirstShootDelay(wave.firstShootDelay[j]);\r\n    //                             }\r\n    //                             enemy.setStandByTime(0);\r\n    //                             enemy.initTrack(\r\n    //                                 wave.trackGroups,\r\n    //                                 wave.liveParam,\r\n    //                                 enemyPosX,\r\n    //                                 enemyPosY,\r\n    //                                 wave.rotateSpeed\r\n    //                             );\r\n    //                         }\r\n    //                         break;\r\n    //                 }\r\n    //             }\r\n    //         }\r\n\r\n    //         if (wave.enemyNum <= this._waveNumArr[i]) {\r\n    //             this._waveArr.splice(i, 1);\r\n    //             this._waveNumArr.splice(i, 1);\r\n    //             this._waveTimeArr.splice(i, 1);\r\n    //             this._waveActionArr.splice(i, 1);\r\n    //             i--;\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    /**\r\n     * 更新普通敌人生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    // private _updateNorEnemys(deltaTime: number): boolean {\r\n    //     if (this._bEnemyNorCreateAble) {\r\n    //         if (this._waveIndex >= this._curEnemyAction!.enemyNorIDs.length) {\r\n    //             this._waveIndex = 0;\r\n    //             return false;\r\n    //         }\r\n\r\n    //         const waveID = this._curEnemyAction!.enemyNorIDs[this._waveIndex];\r\n    //         this._waveCreateTime += deltaTime;\r\n\r\n    //         const waveDatas = this.getNorWaveDatas(waveID);\r\n    //         if (!waveDatas) {\r\n    //             return false;\r\n    //         }\r\n    //         console.log(`ybgg waveID:${waveID} waveDatas length:${waveDatas.length}`);\r\n    //         for (let i = 0; i < waveDatas!.length; i++) {\r\n    //             const wave = waveDatas![i];\r\n    //             if (\r\n    //                 !Tools.arrContain(this._waveIndexOver, i) &&\r\n    //                 this._waveCreateTime >= wave.groupInterval\r\n    //             ) {\r\n    //                 this._waveArr.push(wave);\r\n    //                 this._waveNumArr.push(0);\r\n    //                 this._waveTimeArr.push(0);\r\n    //                 this._waveActionArr.push(this._curEnemyAction);\r\n    //                 this._waveIndexOver.push(i);\r\n    //             }\r\n    //         }\r\n\r\n    //         if (this._waveIndexOver.length >= waveDatas!.length) {\r\n    //             this._waveIndexOver.splice(0);\r\n    //             this._waveCreateTime = 0;\r\n    //             this._waveIndex++;\r\n    //         }\r\n    //     }\r\n\r\n    //     return true;\r\n    // }\r\n\r\n    /**\r\n     * 更新 Boss 生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateBoss(deltaTime: number): void {\r\n        if (this._bossToAddArr.length > 0 && GameIns.enemyManager.isEnemyOver()){\r\n            this._bossCreateTime += deltaTime;\r\n            if (this._bossCreateTime > this._bossCreateDelay) {\r\n                const bossData = this._bossToAddArr[0];\r\n                GameIns.bossManager.addBoss(bossData.enemyNorIDs[0]);\r\n                this._bossToAddArr.splice(0, 1);\r\n                GameIns.battleManager.bossWillEnter();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查敌人是否全部结束\r\n     * @returns 是否所有敌人都已结束\r\n     */\r\n    isEnemyOver(): boolean {\r\n        //return this._enemyOver && this._waveArr.length === 0 && this._bossToAddArr.length === 0;\r\n        return this._activeWaves.length <= 0 && this._bossToAddArr.length === 0;\r\n    }\r\n}"]}