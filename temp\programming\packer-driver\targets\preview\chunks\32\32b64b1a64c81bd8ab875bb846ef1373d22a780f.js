System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, SingletonBase, GameIns, WaveManager, _crd;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../../../../scripts/core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyWave(extras) {
    _reporterNs.report("EnemyWave", "../data/EnemyWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStageData(extras) {
    _reporterNs.report("StageData", "../data/StageData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "../wave/Wave", _context.meta, extras);
  }

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a93d1u5aJFFpIGyblYeA0H7", "WaveManager", undefined);

      __checkObsolete__(['v2', 'warn']);

      _export("default", WaveManager = class WaveManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        get enemyCreateAble() {
          return this._bEnemyCreateAble;
        }

        set enemyCreateAble(value) {
          this._bEnemyCreateAble = value;
        }

        constructor() {
          super(); // this.initConfig();

          this._waveNorDatasMap = new Map();
          // 波次配表数据
          this._bEnemyCreateAble = false;
          //是否可以创建敌机
          this._bEnemyNorCreateAble = false;
          //是否可以创建普通敌机
          // private _waves: Map<string, Wave> = new Map();
          this._activeWaves = [];
          //boss
          this._bossCreateDelay = 0;
          this._bossCreateTime = 0;
          this._bossToAddArr = [];
          this._bShowBossWarning = false;
        }

        reset() {
          this._bEnemyCreateAble = false;
          this._bEnemyNorCreateAble = false; // this._waveActionArr = [];
          // this._waveNumArr = [];
          // this._waveTimeArr = [];
          // this._waves.clear();

          this._activeWaves = [];
          this._bShowBossWarning = false;
          this._bossCreateTime = 0;
        }

        setEnemyActions(actions) {// this._enemyActions = actions;
        }

        gameStart() {
          this._bEnemyCreateAble = true;
          this._bEnemyNorCreateAble = true; // this._waveArr = [];
          // this._waveActionArr = [];
          // this._waveNumArr = [];
          // this._waveTimeArr = [];
          // 这里不能清掉，清掉后存在问题: 时序上是先addWaveByLevel，后gameStart
          // this._waves = [];
        }

        getNorWaveDatas(groupID) {
          return this._waveNorDatasMap.get(groupID);
        }

        addWaveByLevel(wave, posX, posY) {
          // this._waves.set(wave.uuid, wave);
          if (wave) {
            wave.trigger(posX, posY);

            this._activeWaves.push(wave);
          }
        } // triggerWaves(waveUuid: string[], posX: number, posY: number) {
        //     waveUuid.forEach((uuid) => {
        //         const wave = this._waves.get(uuid);
        //         if (wave) {
        //             wave.trigger(posX, posY);
        //             this._activeWaves.push(wave);
        //         }
        //     })
        // }


        updateGameLogic(deltaTime) {
          var _this = this;

          return _asyncToGenerator(function* () {
            // this._updateCurAction(deltaTime);
            _this._updateWaves(deltaTime);

            _this._updateBoss(deltaTime);
          })();
        }
        /**
         * 更新当前敌人行为
         * @param deltaTime 每帧的时间增量
         */
        // private _updateCurAction(deltaTime: number): void {
        //     if (!this._enemyOver) {
        //         if (this._enemyActionIndex >= (this._enemyActions?.length || 0)) {
        //             this._enemyOver = true;
        //             warn("enemy over");
        //         } else if (this.enemyCreateAble && !this._curEnemyAction) {
        //             const action = this._enemyActions![this._enemyActionIndex];
        //             switch (action.type) {
        //                 case 0:
        //                     this._enemyCreateTime += deltaTime;
        //                     if (
        //                         this._enemyCreateTime >= action.enemyNorInterval ||
        //                         (this._waveArr.length === 0 && GameIns.enemyManager.getNormalPlaneCount() === 0)
        //                     ) {
        //                         this._curEnemyAction = action;
        //                     }
        //                     break;
        //                 default:
        //                     if (action.type >= 100) {
        //                         console.warn("Boss stage", action.type, action.enemyNorIDs[0]);
        //                         this._bossCreateDelay = action.enemyNorInterval;
        //                         this._bossToAddArr.push(action);
        //                         this._enemyActionIndex++;
        //                     }
        //             }
        //         }
        //     }
        // }

        /**
         * 更新敌人逻辑
         * @param deltaTime 每帧的时间增量
         */
        // private async _updateEnemy(deltaTime: number) {
        //     // await this._updateEnemyCreate(deltaTime);
        //     if (this._curEnemyAction) {
        //         if (!this._updateNorEnemys(deltaTime)) {
        //             this._curEnemyAction = null;
        //             this._enemyActionIndex++;
        //             this._enemyCreateTime = 0;
        //         }
        //     }
        // }


        _updateWaves(deltaTime) {
          var dtInMiliseconds = deltaTime * 1000;

          for (var i = this._activeWaves.length - 1; i >= 0; i--) {
            var wave = this._activeWaves[i];
            wave.tick(dtInMiliseconds);

            if (wave.isCompleted) {
              this._activeWaves.splice(i, 1);
            }
          }

          console.log("ybgg _updateWaves left waves: " + this._activeWaves.length);
        }
        /**
         * 更新敌人生成逻辑
         * @param deltaTime 每帧的时间增量
         */
        // private async _updateEnemyCreate(deltaTime: number): Promise<void> {
        //     for (let i = 0; i < this._waveArr.length; i++) {
        //         const wave = this._waveArr[i];
        //         this._waveTimeArr[i] += deltaTime;
        //         const currentEnemyCount = this._waveNumArr[i];
        //         let posX = GameConst.EnemyPos.x;
        //         let posY = GameConst.EnemyPos.y;
        //         if (wave.bSetStartPos) {
        //             posX += wave.startPosX;
        //             posY += wave.startPosY;
        //         }
        //         for (let j = currentEnemyCount; j < wave.enemyNum; j++) {
        //             if (wave.enemyInterval * (j + 1) < this._waveTimeArr[i]) {
        //                 this._waveNumArr[i]++;
        //                 let enemy:EnemyPlane|null;
        //                 const enemyPosX = posX + wave.posDX * (j + 1);
        //                 const enemyPosY = posY + wave.posDY * (j + 1);
        //                 switch (wave.type) {
        //                     case 0:
        //                         enemy = await GameIns.enemyManager.addPlane(wave.enemyID);
        //                         if (enemy) {
        //                             if (j < wave.firstShootDelay.length) {
        //                                 enemy.setFirstShootDelay(wave.firstShootDelay[j]);
        //                             }
        //                             enemy.setStandByTime(0);
        //                             enemy.initTrack(
        //                                 wave.trackGroups,
        //                                 wave.liveParam,
        //                                 enemyPosX,
        //                                 enemyPosY,
        //                                 wave.rotateSpeed
        //                             );
        //                         }
        //                         break;
        //                 }
        //             }
        //         }
        //         if (wave.enemyNum <= this._waveNumArr[i]) {
        //             this._waveArr.splice(i, 1);
        //             this._waveNumArr.splice(i, 1);
        //             this._waveTimeArr.splice(i, 1);
        //             this._waveActionArr.splice(i, 1);
        //             i--;
        //         }
        //     }
        // }

        /**
         * 更新普通敌人生成逻辑
         * @param deltaTime 每帧的时间增量
         */
        // private _updateNorEnemys(deltaTime: number): boolean {
        //     if (this._bEnemyNorCreateAble) {
        //         if (this._waveIndex >= this._curEnemyAction!.enemyNorIDs.length) {
        //             this._waveIndex = 0;
        //             return false;
        //         }
        //         const waveID = this._curEnemyAction!.enemyNorIDs[this._waveIndex];
        //         this._waveCreateTime += deltaTime;
        //         const waveDatas = this.getNorWaveDatas(waveID);
        //         if (!waveDatas) {
        //             return false;
        //         }
        //         console.log(`ybgg waveID:${waveID} waveDatas length:${waveDatas.length}`);
        //         for (let i = 0; i < waveDatas!.length; i++) {
        //             const wave = waveDatas![i];
        //             if (
        //                 !Tools.arrContain(this._waveIndexOver, i) &&
        //                 this._waveCreateTime >= wave.groupInterval
        //             ) {
        //                 this._waveArr.push(wave);
        //                 this._waveNumArr.push(0);
        //                 this._waveTimeArr.push(0);
        //                 this._waveActionArr.push(this._curEnemyAction);
        //                 this._waveIndexOver.push(i);
        //             }
        //         }
        //         if (this._waveIndexOver.length >= waveDatas!.length) {
        //             this._waveIndexOver.splice(0);
        //             this._waveCreateTime = 0;
        //             this._waveIndex++;
        //         }
        //     }
        //     return true;
        // }

        /**
         * 更新 Boss 生成逻辑
         * @param deltaTime 每帧的时间增量
         */


        _updateBoss(deltaTime) {
          if (this._bossToAddArr.length > 0 && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.isEnemyOver()) {
            this._bossCreateTime += deltaTime;

            if (this._bossCreateTime > this._bossCreateDelay) {
              var bossData = this._bossToAddArr[0];
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).bossManager.addBoss(bossData.enemyNorIDs[0]);

              this._bossToAddArr.splice(0, 1);

              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.bossWillEnter();
            }
          }
        }
        /**
         * 检查敌人是否全部结束
         * @returns 是否所有敌人都已结束
         */


        isEnemyOver() {
          //return this._enemyOver && this._waveArr.length === 0 && this._bossToAddArr.length === 0;
          return this._activeWaves.length <= 0 && this._bossToAddArr.length === 0;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=32b64b1a64c81bd8ab875bb846ef1373d22a780f.js.map