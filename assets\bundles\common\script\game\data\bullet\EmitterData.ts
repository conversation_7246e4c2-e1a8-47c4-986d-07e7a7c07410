
import { _decorator, error, v2, Vec2, Prefab } from "cc";
import { EventGroupData } from "./EventGroupData";
import { ExpressionValue } from "./ExpressionValue";
const { ccclass, property } = _decorator;

/**
 * 发射器数据
 * 所有时间相关的，单位都是毫秒(ms)
 */
@ccclass("EmitterData")
export class EmitterData {
    // @property({displayName: '发射器名称'})
    // name : string = '';                // uid 
    @property({displayName: '是否仅在屏幕内发射', group: '基础属性'})
    isOnlyInScreen : boolean = true;   // 仅在屏幕内才发射

    @property({visible:false})
    initialDelay : ExpressionValue = new ExpressionValue('0');
    @property({displayName: '初始延迟', group: '基础属性'})
    get initialDelayStr(): string { return this.initialDelay.raw; }
    set initialDelayStr(value: string) { this.initialDelay.raw = value; }

    @property({visible:false})
    public emitDuration : ExpressionValue = new ExpressionValue('10000');
    @property({displayName: '发射器持续时间', group: '基础属性'})
    public get emitDurationStr(): string { return this.emitDuration.raw; }
    public set emitDurationStr(value: string) { this.emitDuration.raw = value; }

    @property({displayName: '是否预热', tooltip: '预热xxx', group: '基础属性'})
    isPreWarm : boolean = false;       // 是否预热
    @property({visible:false})
    public preWarmDuration : ExpressionValue = new ExpressionValue('0');
    @property({displayName: '预热持续时长', group: '基础属性'})
    public get preWarmDurationStr(): string { return this.preWarmDuration.raw; }
    public set preWarmDurationStr(value: string) { this.preWarmDuration.raw = value; }

    // 预热特效:(这个是否用prefab，直接包含音效、音量等信息)
    @property({type: Prefab, displayName: '预热特效', group: '基础属性'})
    preWarmEffect : Prefab = null!; 
    // @property({displayName: '预热音效'})
    // preWarmSound : string;             // 预热音效
    
    @property({displayName: '是否循环', group: '基础属性'})
    isLoop : boolean = true;           // 是否循环
    // 循环间隔
    @property({visible:false})
    public loopInterval : ExpressionValue = new ExpressionValue('0.0');
    @property({displayName: '循环间隔', tooltip: "发射器持续时间结束->循环间隔->预热", group: '基础属性'})
    public get loopIntervalStr(): string { return this.loopInterval.raw; }
    public set loopIntervalStr(value: string) { this.loopInterval.raw = value; }

    @property({visible:false})
    public emitInterval : ExpressionValue = new ExpressionValue('1000');
    @property({displayName: '开火间隔', group: '基础属性'})
    public get emitIntervalStr(): string { return this.emitInterval.raw; }
    public set emitIntervalStr(value: string) { this.emitInterval.raw = value; }

    // 用来修改子弹初始速度的乘数(备用)
    @property({visible:false})
    public emitPower : ExpressionValue = new ExpressionValue('1.0');
    @property({displayName: '发射速度', group: '基础属性'})
    public get emitPowerStr(): string { return this.emitPower.raw; }
    public set emitPowerStr(value: string) { this.emitPower.raw = value; }

    // 单次发射数量
    @property({visible:false})
    public perEmitCount : ExpressionValue = new ExpressionValue('1');
    @property({displayName: '单次发射数量', group: '基础属性'})
    public get perEmitCountStr(): string { return this.perEmitCount.raw; }
    public set perEmitCountStr(value: string) { this.perEmitCount.raw = value; }

    // 单次发射多个子弹时的间隔
    @property({visible:false})
    public perEmitInterval : ExpressionValue = new ExpressionValue('0.0');
    @property({displayName: '单次发射多个子弹时的间隔', group: '基础属性'})
    public get perEmitIntervalStr(): string { return this.perEmitInterval.raw; }
    public set perEmitIntervalStr(value: string) { this.perEmitInterval.raw = value; }
     
    // 单次发射多个子弹时的x偏移    
    @property({visible:false})
    public perEmitOffsetX : ExpressionValue = new ExpressionValue('0.0');
    @property({displayName: '单次发射多个子弹时的x偏移', group: '基础属性'})
    public get perEmitOffsetXStr(): string { return this.perEmitOffsetX.raw; }
    public set perEmitOffsetXStr(value: string) { this.perEmitOffsetX.raw = value; }

    // 发射方向: 0朝右, 90朝上, 180朝左, 270朝下(or -90)
    @property({visible:false})
    public angle : ExpressionValue = new ExpressionValue('90');
    @property({displayName: '弹道角度', tooltip:"决定发射方向", group: '基础属性'})
    public get angleStr(): string { return this.angle.raw; }
    public set angleStr(value: string) { this.angle.raw = value; }

    // 发射条数(弹道数量)
    @property({visible:false})
    public count : ExpressionValue = new ExpressionValue('1');
    @property({displayName: '弹道数量', tooltip:"决定发射条数", group: '基础属性'})
    public get countStr(): string { return this.count.raw; }
    public set countStr(value: string) { this.count.raw = value; }

    // 发射范围(弧度范围)
    @property({visible:false})
    public arc : ExpressionValue = new ExpressionValue('60');
    @property({displayName: '发射范围', group: '基础属性'})
    public get arcStr(): string { return this.arc.raw; }
    public set arcStr(value: string) { this.arc.raw = value; }

    // 发射半径
    @property({visible:false})
    public radius : ExpressionValue = new ExpressionValue('1.0');
    @property({displayName: '发射半径', group: '基础属性'})
    public get radiusStr(): string { return this.radius.raw; }
    public set radiusStr(value: string) { this.radius.raw = value; }

    @property({type:Prefab, displayName: '发射特效', group: '基础属性'})
    emitEffect : Prefab = null!;               // 发射特效(多个的话建议做到prefab上?) 包含音效?

    @property({type:[EventGroupData], displayName: '事件组', group: '事件组'})
    eventGroupData: EventGroupData[] = [];

    static fromJSON(json: any): EmitterData {
        const data = new EmitterData();
        if (json) {
            Object.assign(data, json);
            data.eventGroupData = (json.eventGroupData || []).map(EventGroupData.fromJSON);
        }

        return data;
    }
}